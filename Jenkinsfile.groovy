/**  =========================  标准配置  =============================  **/

import com.trax.devops.publish.*
import hudson.model.*

@Library('trax-cicd-lib') _

//发布的镜像
def imageMap = [:]

def group = 'dockerimg.lenztechretail.com'
def nameSpace = 'tope'

//maven版本,用来构建的脚本，确定 snapshot / release
def mavenVersion = "release"

//版本号
//String tag = "${BRANCH_NAME}.${MAIN_VERSION}.${SMALL_VERSION}.${BUILD_NUMBER}"
String tag = "${BUILD_TAG}"

//环境列表
def deployEnvList = ['dev']


/**  =========================  应用自定义  =============================  **/

//应用前缀
def appPrefix = "teemo"
//项目名
def projectName = "${appPrefix}-parent"
//服务所在目录，会根据该目录结合appName寻找jar包进行docker镜像构建
def serviceDir = "${appPrefix}-web"
//发布的应用
def appNames = ["${appPrefix}-service","${appPrefix}-manager"]
//git仓库地址
//def gitUrl = "*************:ppz_bj/${projectName}.git"
def gitUrl = "************************:rd-java/teemo-parent.git"
//通知版本号的dataID
def nacosDataId = "${projectName}.yml"

/**  =========================  发布环境（仅包含发布时使用）  =============================  **/

pipeline {
    agent any

    stages {

        stage('Git Checkout') {
            steps {
                script {
                    echo "Checkout...${BRANCH_NAME}"
                    checkout scm
                }
            }
        }

        stage('Branch Check') {
            steps {
                script {
                    checkTag(BRANCH_NAME)
                }
            }
        }

        stage('Input Env') {
            steps {
                timeout(time: 1, unit: 'MINUTES') {
                    script {
                        env.DEPLOY_ENV = input message: '选择部署的环境', ok: 'deploy',
                                parameters: [choice(name: 'DEPLOY_ENV', choices: ['dev', 'test', 'uat' ,'prod'], description: '选择部署环境')]
                        println("build ${env.DEPLOY_ENV} env")
                        deployEnvList = ["${env.DEPLOY_ENV}"]
                    }
                }
            }
        }

        stage('Maven Build') {
            steps {
                script {
                    if (deployEnvList == ['prod'] && "${BRANCH_NAME}".startsWith("master")){
                        mavenVersion = "release"
                    }else{
                        mavenVersion = "snapshot"
                    }
                    maven(mavenVersion)
                }
            }
        }

        stage('Build and Push Image') {
            steps {
                script {
                    customDocker.build(group, nameSpace, tag, serviceDir, appNames, imageMap)
                }
            }
        }

        stage("Publish Version") {
            steps {
                script {
                    PublishImage.noticeNacos(nacosDataId, imageMap, deployEnvList)
                    Notice.noticeK8sNacos(nacosDataId, imageMap, deployEnvList)
                }
            }
        }

        stage('GIT ARCHIVING') {
            steps {
                script {
                    if (deployEnvList == ['prod'] && "${BRANCH_NAME}".startsWith("master")) {
                        customGit.tag(gitUrl)
                    }
                }
            }
        }

    }
}

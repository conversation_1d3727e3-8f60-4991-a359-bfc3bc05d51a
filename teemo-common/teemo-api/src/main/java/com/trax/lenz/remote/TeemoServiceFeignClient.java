package com.trax.lenz.remote;

import com.trax.lenz.common.core.domain.R;
import com.trax.lenz.dto.request.IdentifyParameterReq;
import com.trax.lenz.dto.response.IdentifyParameterResp;
import com.trax.lenz.gabriel.dto.request.AddAppealReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @ClassName TeemoServiceFeignClient
 * @Description: teemo-service服务
 * <AUTHOR>
 * @Date 2022-12-05-14:47
 **/
@FeignClient(value = "teemo-service", path = "/business")
public interface TeemoServiceFeignClient {

    @RequestMapping(value = "/getCustomerInfo")
    R<IdentifyParameterResp> getCustomerInfo(@RequestBody IdentifyParameterReq req);

    @RequestMapping(value = "/appeal/callback", method = RequestMethod.POST)
    R callback(@RequestBody AddAppealReq req);

}

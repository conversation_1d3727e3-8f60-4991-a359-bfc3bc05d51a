<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>teemo-common</artifactId>
    <groupId>com.trax.lenz</groupId>
    <version>1.0.70</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>teemo-api</artifactId>

  <dependencies>

    <dependency>
      <groupId>com.trax.lenz</groupId>
      <artifactId>teemo-domain</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-openfeign-core</artifactId>
      <version>3.0.2</version>
    </dependency>

    <dependency>
      <groupId>com.trax.lenz.common</groupId>
      <artifactId>common-security</artifactId>
    </dependency>

    <dependency>
      <groupId>com.trax.lenz</groupId>
      <artifactId>gabriel-api</artifactId>
      <version>1.0.50</version>
    </dependency>

  </dependencies>

</project>

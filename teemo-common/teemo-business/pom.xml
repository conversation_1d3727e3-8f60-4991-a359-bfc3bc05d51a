<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>teemo-common</artifactId>
        <groupId>com.trax.lenz</groupId>
        <version>1.0.70</version>
    </parent>
    <artifactId>teemo-business</artifactId>

    <dependencies>

        <!-- domain模块 -->
        <dependency>
            <groupId>com.trax.lenz</groupId>
            <artifactId>teemo-domain</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>

        <dependency>
            <groupId>com.trax.lenz.common</groupId>
            <artifactId>common-util</artifactId>
            <!--            <exclusions>-->
            <!--                <exclusion>-->
            <!--                    <groupId>com.trax.lenz.common</groupId>-->
            <!--                    <artifactId>redis-spring-boot-starter</artifactId>-->
            <!--                </exclusion>-->
            <!--            </exclusions>-->
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.6</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.aliyun.oss/aliyun-sdk-oss -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.3.0</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun.mns</groupId>
            <artifactId>aliyun-sdk-mns</artifactId>
            <version>1.1.9</version>
            <classifier>jar-with-dependencies</classifier>
        </dependency>

        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.trax.lenz.api</groupId>
            <artifactId>api-api</artifactId>
            <version>1.6.244</version>
        </dependency>

        <dependency>
            <groupId>com.trax.lenz.paladin</groupId>
            <artifactId>paladin-api</artifactId>
            <version>1.0.73</version>
        </dependency>

        <dependency>
            <groupId>org.ssssssss</groupId>
            <artifactId>magic-api-spring-boot-starter</artifactId>
            <version>2.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.trax.lenz</groupId>
            <artifactId>gabriel-api</artifactId>
            <version>1.0.50</version>
        </dependency>

        <dependency>
            <groupId>com.trax.lenz.kylin</groupId>
            <artifactId>kylin-api</artifactId>
            <version>1.0.119</version>
        </dependency>

        <dependency>
            <groupId>com.trax.lenz</groupId>
            <artifactId>soraka-api</artifactId>
            <version>1.0.10</version>
        </dependency>

        <dependency>
            <groupId>com.trax.lenz.azathoth</groupId>
            <artifactId>azathoth-api</artifactId>
            <version>1.0.65</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.trax.lenz.message.protobuf</groupId>
            <artifactId>brainface</artifactId>
            <version>1.0.29</version>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <version>3.19.2</version>
        </dependency>

        <dependency>
            <groupId>com.trax.lenz.message.protobuf</groupId>
            <artifactId>brain-servicepb</artifactId>
            <version>4.1.11</version>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-netty-shaded</artifactId>
            <version>1.38.0</version>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-protobuf</artifactId>
            <version>1.38.0</version>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-stub</artifactId>
            <version>1.38.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.19.2</version>
        </dependency>

        <dependency>
            <groupId>com.trax.lenz.mario</groupId>
            <artifactId>mario-api</artifactId>
            <version>1.0.27</version>
        </dependency>

    </dependencies>

</project>

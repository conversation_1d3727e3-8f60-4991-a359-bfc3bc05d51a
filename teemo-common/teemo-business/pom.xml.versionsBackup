<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <artifactId>teemo-common</artifactId>
    <groupId>com.trax.lenz</groupId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <artifactId>teemo-business</artifactId>

  <properties>
    <knife4j.version>3.0.3</knife4j.version>
  </properties>

  <dependencies>

    <!-- domain模块 -->
    <dependency>
      <groupId>com.trax.lenz</groupId>
      <artifactId>teemo-domain</artifactId>
      <version>${project.parent.version}</version>
    </dependency>

    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-extension</artifactId>
    </dependency>

    <dependency>
      <groupId>com.trax.lenz.common</groupId>
      <artifactId>common-util</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>

    <dependency>
      <groupId>com.trax.lenz.api</groupId>
      <artifactId>api-domain</artifactId>
      <version>1.6.51</version>
      <exclusions></exclusions>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.6</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/com.aliyun.oss/aliyun-sdk-oss -->
    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
      <version>3.3.0</version>
    </dependency>

    <dependency>
      <groupId>org.apache.shardingsphere</groupId>
      <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>


  </dependencies>

</project>

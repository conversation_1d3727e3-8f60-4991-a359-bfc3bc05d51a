<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trax.lenz.mapper.SnowbeerResultDetailMapper">

    <update id="updateByVisitCode">
        UPDATE snowbeer_result_detail
        SET response_id = #{responseId,jdbcType=VARCHAR}
        WHERE visit_code = #{visitCode,jdbcType=INTEGER}
    </update>

    <insert id="batchInsert">
        INSERT INTO snowbeer_result_detail (
        visit_code,
        visit_date,
        marketing_center,
        sales_region,
        business_segment,
        workstation,
        service_line,
        image_url,
        terminal_type,
        manage_scale,
        manage_grade,
        chain_property,
        chain_corporation,
        chain_brand,
        terminal_state,
        terminal_coding,
        terminal_name,
        first_img_type,
        second_img_type,
        third_img_type,
        visit_type,
        person_code,
        person_name,
        agent_code,
        agent_name,
        customer_image_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.visitCode,jdbcType=VARCHAR},
            #{item.visitDate,jdbcType=VARCHAR},
            #{item.marketingCenter,jdbcType=VARCHAR},
            #{item.salesRegion,jdbcType=VARCHAR},
            #{item.businessSegment,jdbcType=VARCHAR},
            #{item.workstation,jdbcType=VARCHAR},
            #{item.serviceLine,jdbcType=VARCHAR},
            #{item.imageUrl,jdbcType=VARCHAR},
            #{item.terminalType,jdbcType=VARCHAR},
            #{item.manageScale,jdbcType=VARCHAR},
            #{item.manageGrade,jdbcType=VARCHAR},
            #{item.chainProperty,jdbcType=VARCHAR},
            #{item.chainCorporation,jdbcType=VARCHAR},
            #{item.chainBrand,jdbcType=VARCHAR},
            #{item.terminalState,jdbcType=VARCHAR},
            #{item.terminalCoding,jdbcType=VARCHAR},
            #{item.terminalName,jdbcType=VARCHAR},
            #{item.firstImgType,jdbcType=VARCHAR},
            #{item.secondImgType,jdbcType=VARCHAR},
            #{item.thirdImgType,jdbcType=VARCHAR},
            #{item.visitType,jdbcType=VARCHAR},
            #{item.personCode,jdbcType=VARCHAR},
            #{item.personName,jdbcType=VARCHAR},
            #{item.agentCode,jdbcType=VARCHAR},
            #{item.agentName,jdbcType=VARCHAR},
            #{item.customerImageId,jdbcType=VARCHAR})
        </foreach>
    </insert>

</mapper>

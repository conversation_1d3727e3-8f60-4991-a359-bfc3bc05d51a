package com.trax.lenz.common.business.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Redis 计数器工具类
 */
@Slf4j
@Component
public class RedisCounterUtil {

    @Resource(name = "counterRedisTemplate")
    private RedisTemplate<String, Integer> redisTemplate;

    private static final String COUNTER_KEY_PREFIX = "_mn_repeat_backlog";

    /**
     * 设置计数器初始值
     *
     * @param key          计数器键名
     * @param initialValue 初始值
     * @param expireTime   过期时间（秒）
     * @return 是否设置成功
     */
    public boolean setCounter(String key, int initialValue, long expireTime) {
        try {
            String redisKey = COUNTER_KEY_PREFIX + key;
            // 检查key是否已存在
            if (Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))) {
                log.info("计数器key已存在，不进行写入操作, key: {}", key);
                return false;
            }
            redisTemplate.opsForValue().set(redisKey, initialValue, expireTime, TimeUnit.SECONDS);
            return true;
        } catch (Exception e) {
            log.error("设置计数器初始值失败, key: {}, initialValue: {}, expireTime: {}", key, initialValue, expireTime, e);
            return false;
        }
    }

    /**
     * 增加计数器值（每次+1）
     *
     * @param key 计数器键名
     * @return 增加后的值
     */
    public int incrementCounter(String key) {
        try {
            String redisKey = COUNTER_KEY_PREFIX + key;
            Long value = redisTemplate.opsForValue().increment(redisKey);
            return value != null ? value.intValue() : 0;
        } catch (Exception e) {
            log.error("增加计数器值失败, key: {}", key, e);
            return 0;
        }
    }

    /**
     * 获取计数器当前值
     *
     * @param key 计数器键名
     * @return 当前值
     */
    public int getCounterValue(String key) {
        try {
            String redisKey = COUNTER_KEY_PREFIX + key;
            Object value = redisTemplate.opsForValue().get(redisKey);
            if (value != null) {
                if (value instanceof Integer) {
                    return (Integer) value; // 如果已经是 Integer，直接返回
                } else if (value instanceof Double) {
                    return ((Double) value).intValue(); // 如果是 Double，转换为 Integer
                } else if (value instanceof String) {
                    try {
                        return Integer.parseInt((String) value); // 如果是字符串，尝试解析为 Integer
                    } catch (NumberFormatException e) {
                        throw new IllegalArgumentException("无法将字符串值转换为整数: " + value, e);
                    }
                } else {
                    throw new ClassCastException("不支持的类型: " + value.getClass().getName());
                }
            }
            return 0;
        } catch (Exception e) {
            log.error("获取计数器当前值失败, key: {}", key, e);
            return 0;
        }
    }
} 
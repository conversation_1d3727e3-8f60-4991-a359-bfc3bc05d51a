package com.trax.lenz.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 客户申诉请求记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppealRequest extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private String appealId;

    private String responseId;

    private String customerRequestId;

    private String taskId;

    private String taskName;

    private Integer type;

    private Integer status;

    private Date createTime;

    private Integer test;

    private String reqContent;

    private String gabrielReqContent;

    private Date auditFinishTime;

    private String gabrielRespContent;

    private Integer callbackStatus;

    private Integer counts;

    private Integer retryEnabled;

    private String callbackUrl;

    private String callbackType;

    private String callbackParam;

    private String callbackHttpCode;

    private String callbackResponse;

    private String batchAppealId;

    private Date updateTime;

}

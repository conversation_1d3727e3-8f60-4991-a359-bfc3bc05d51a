package com.trax.lenz.service;

import com.google.protobuf.util.JsonFormat;
import com.trax.lenz.message.protobuf.brainface.Interface;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.net.URI;

/**
 * @ClassName ProtoBufService
 * @Description:
 * <AUTHOR>
 * @Date 2023-09-13-17:27
 **/

@Slf4j
@Service
public class ProtoBufService {

    /**
     * 处理AI结果报文
     *
     * @return Interface.FullAIResponse
     */
    public Interface.FullAIResponse getFullAiResponse(String url) {
        // 解析AI报文
        Interface.FullAIResponse fullAiResponse;
        try {
            if (StringUtils.isNotBlank(url) && url.endsWith(".protobuf")) {
                byte[] bytes = IOUtils.toByteArray(new URI(url));
                fullAiResponse = Interface.FullAIResponse.parseFrom(bytes);
            } else {
                byte[] bytes = IOUtils.toByteArray(new URI(url));
                Interface.FullAIResponse.Builder builder = Interface.FullAIResponse.newBuilder();
                JsonFormat.parser().ignoringUnknownFields().merge(new String(bytes), builder);
                fullAiResponse = builder.build();
            }
            return fullAiResponse;
        } catch (Exception e) {
            log.error("读取Protobuf—json文件异常!", e);
        }
        return null;
    }

    /**
     * 处理AI请求报文
     *
     * @param url
     * @return Interface.FullAIRequest
     */
    public Interface.FullAIRequest getFullAiRequest(String url) {

        Interface.FullAIRequest fullAiRequest;
        try {
            if (StringUtils.isNotBlank(url) && url.endsWith(".protobuf")) {
                byte[] bytes = IOUtils.toByteArray(new URI(url));
                fullAiRequest = Interface.FullAIRequest.parseFrom(bytes);
            } else {
                byte[] bytes = IOUtils.toByteArray(new URI(url));
                Interface.FullAIRequest.Builder builder = Interface.FullAIRequest.newBuilder();
                JsonFormat.parser().ignoringUnknownFields().merge(new String(bytes), builder);
                fullAiRequest = builder.build();
            }
            return fullAiRequest;
        } catch (Exception e) {
            log.error("读取Protobuf—json文件异常!", e);
        }
        return null;

    }

}

package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trax.lenz.entity.PecRepeatRequest;
import com.trax.lenz.mapper.PecRepeatRequestMapper;
import com.trax.lenz.service.IPecRepeatRequestService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 统一客户查重请求记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
public class PecRepeatRequestServiceImpl extends ServiceImpl<PecRepeatRequestMapper, PecRepeatRequest> implements IPecRepeatRequestService {

    public PecRepeatRequest getOne(String checkRepeatId) {
        LambdaQueryWrapper<PecRepeatRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PecRepeatRequest::getCheckRepeatId, checkRepeatId);
        return this.getOne(wrapper);
    }

    public void update(String checkRepeatId, PecRepeatRequest pecRepeatRequest) {
        LambdaQueryWrapper<PecRepeatRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PecRepeatRequest::getCheckRepeatId, checkRepeatId);
        update(pecRepeatRequest, wrapper);
    }

}

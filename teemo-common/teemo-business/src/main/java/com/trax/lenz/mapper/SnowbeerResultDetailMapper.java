package com.trax.lenz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trax.lenz.entity.SnowbeerResultDetail;
import feign.Param;

import java.util.List;

/**
 * <p>
 * 雪花贵州POC原始结果记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
public interface SnowbeerResultDetailMapper extends BaseMapper<SnowbeerResultDetail> {

    void updateByVisitCode(@Param("visitCode") String visitCode, @Param("responseId") String responseId);

    /**
     * 批量插入
     *
     * @param snowbeerResultDetailList
     */
    void batchInsert(List<SnowbeerResultDetail> snowbeerResultDetailList);
}

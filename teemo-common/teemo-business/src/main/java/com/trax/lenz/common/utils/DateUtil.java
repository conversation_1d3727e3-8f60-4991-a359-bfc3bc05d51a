package com.trax.lenz.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 日期工具类,日期转字符串,字符串转日期,日期计算等
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/8 13:41
 * @since JDK 1.8
 */
public class DateUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);

    public static final String DATE_FORMAT_1 = "yyyy-MM-dd";
    public static final String DATE_FORMAT_2 = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT_3 = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String DATE_FORMAT_4 = "yyyyMMddHHmmss";
    public static final String DATE_FORMAT_5 = "yyyy";
    public static final String DATE_FORMAT_6 = "HH:mm";
    public static final String DATE_FORMAT_7 = "yyyy/MM/dd HH:mm";
    public static final String DATE_FORMAT_8 = "yyyyMMdd";
    public static final String DATE_FORMAT_9 = "yyyyMMddHHmm";
    public static final String DATE_FORMAT_10 = "yyyy/MM/dd";
    public static final String DATE_FORMAT_11 = "yyyy-MM-dd HH:mm:00";
    public static final String DATE_FORMAT_12 = "yyyy-MM-dd 00:00:00";
    public static final String DATE_FORMAT_13 = "yyyy-MM-dd HH:mm";
    public static final String DATE_FORMAT_14 = "MM";
    public static final String DATE_FORMAT_15 = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String DATE_FORMAT_16 = "HH";
    public static final String DATE_FORMAT_17 = "yyyyMMddHHmmssSSS";
    public static final String DATE_FORMAT_18 = "dd";
    public static final String DATE_FORMAT_19 = "HH:mm:ss.SSS";
    public static final String DATE_FORMAT_20 = "HH:mm:ss";
    public static final String DATE_FORMAT_21 = "yyyyMM";
    public static final String DATE_FORMAT_22 = "yyyyMMdd/HH/mm";

    public static final String START_TIME_POSTFIX = " 00:00:00";
    public static final String END_TIME_POSTFIX = " 23:59:59";

    /**
     * 月份
     */
    private static int JAN = 1;
    private static int MAR = 3;
    private static int APR = 4;
    private static int JUN = 6;
    private static int JUL = 7;
    private static int SEP = 9;
    private static int OCT = 10;
    private static int DEC = 12;

    /**
     * 获取今天还剩下多少秒(int)
     *
     * @return
     */
    public static long getSeconds() {
        Calendar curDate = Calendar.getInstance();
        Calendar tommorowDate = new GregorianCalendar(curDate
                .get(Calendar.YEAR), curDate.get(Calendar.MONTH), curDate
                .get(Calendar.DATE) + 1, 0, 0, 0);
        return (tommorowDate.getTimeInMillis() - curDate.getTimeInMillis()) / 1000;
    }

    /**
     * SimpleDateFormat线程不安全,每次new开销太大
     * @param format
     * @return
     */
    private static SimpleDateFormat getSimpleDateFormat(String format) {
        ThreadLocal<SimpleDateFormat> threadLocal = new ThreadLocal<SimpleDateFormat>() {
            @Override
            protected SimpleDateFormat initialValue() {
                return new SimpleDateFormat(format);
            }
        };
        return threadLocal.get();
        //return new SimpleDateFormat(format);
    }

    /**
     * 根据类型，返回当前日期的格式化字符串
     *
     * @param formatStr
     */
    public static String getFormatDate(String formatStr) {
        Date date = new Date();
        SimpleDateFormat dateformat = getSimpleDateFormat(formatStr);
        return dateformat.format(date);
    }

    /**
     * 根据类型，返回参数日期的格式化字符串
     *
     * @param date
     * @param formatStr
     */
    public static String getFormatDate(Date date, String formatStr) {
        SimpleDateFormat dateformat = getSimpleDateFormat(formatStr);
        return dateformat.format(date);
    }

    /**
     * Date ==> String 指定时区
     * @param date
     * @param formatStr
     * @param locale
     * @return
     */
    public static String getFormatDate(Date date, String formatStr, Locale locale) {
        if (date == null) {
            return null;
        }
        if (StringUtils.isNotBlank(formatStr)) {
            locale = locale != null ? locale : Locale.US;
            SimpleDateFormat formater = new SimpleDateFormat(formatStr, locale);
            return formater.format(date);
        } else {
            return getFormatDate(DateUtil.DATE_FORMAT_2);
        }
    }

    /**
     * 格式化字符串成指定日期格式
     *
     * @param dateStr   字符串类型时间
     * @param formatStr 日期格式
     * @return
     * @throws ParseException
     */
    public static Date getFormatDate(String dateStr, String formatStr) throws ParseException {
        if (StringUtils.isNotBlank(dateStr)) {
            return getSimpleDateFormat(formatStr).parse(dateStr);
        } else {
            return null;
        }
    }

    /**
     * 格式化字符串成指定日期格式
     *
     * @param dateStr   字符串类型时间
     * @param formatStr 日期格式
     * @return
     */
    public static Date getFormatDate2(String dateStr, String formatStr) {
        try {
            return getSimpleDateFormat(formatStr).parse(dateStr);
        } catch (Exception e) {
            LOGGER.error("格式化字符串成指定日期格式异常,dateStr:{},formatStr:{}", dateStr, formatStr, e);
        }
        return null;
    }

    /**
     * 把一种字符串日期格式,转换成另一种字符串日期格式
     *
     * @param dateStr         此日期字符串
     * @param formatStr       此字符串日期格式
     * @param targetFormatStr 目标日期格式
     * @return
     */
    public static String getFormatDate(String dateStr, String formatStr, String targetFormatStr) throws ParseException {
        Date thisDate = getSimpleDateFormat(formatStr).parse(dateStr);
        String targetDateStr = getFormatDate(thisDate, targetFormatStr);
        return targetDateStr;
    }

    /**
     * 格式化几天前的日期
     * 格式化后的日期字符串 格式：年-月-日 时:分:秒
     */
    public static String getBeforeDateFormat(int day) {
        long longBeforeDate = System.currentTimeMillis() - (1000 * 60 * 60 * 24 * day);
        Date date = new Date(longBeforeDate);
        return getFormatDate(date, DateUtil.DATE_FORMAT_2);
    }

    /**
     * 与date时间相差day天的时间 格式  年-月-日
     * date 当前时间
     * day 负数 向后推几天
     * day 整数 向前推几天
     */
    public static String getSimpleDate(Date date, int day) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, day);
        return getFormatDate(cal.getTime(), DateUtil.DATE_FORMAT_2);
    }

    /**
     * 与date时间相差hour小时
     * date 当前时间
     * hour 负数 向后推几小时
     * hour 整数 向前推几小时
     */
    public static Date getSimpleDateHour(Date date, int hour) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR, hour);
        return cal.getTime();
    }

    /**
     * 与date时间相差minute分钟
     * date 当前时间
     * minute 负数 向后推几分钟
     * minute 整数 向前推几分钟
     */
    public static Date getSimpleDateMinute(Date date, int minute) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MINUTE, minute);
        return cal.getTime();
    }

    /**
     * 获取两个时间 相差几天
     */
    public static int getDay(Date startTime, Date endTime) {
        return (int) ((endTime.getTime() - startTime.getTime()) / 24 / 60 / 60 / 1000);
    }

    /**
     * 获取两个时间相差几小时
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static int getHour(Date startTime, Date endTime) {
        return (int) ((endTime.getTime() - startTime.getTime()) / 60 / 60 / 1000);
    }

    /**
     * 判断是否>24小时
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static boolean graterThan24(Date startTime, Date endTime) {
        return (endTime.getTime() - startTime.getTime()) > 24 * 60 * 60 * 1000;
    }

    /**
     * 获取两个时间相差多少分钟
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static int getMinute(Date startTime, Date endTime) {
        return (int) ((endTime.getTime() - startTime.getTime()) / 60 / 1000);
    }

    public static int getMonth(Date startTime, Date endTime) {
        Calendar bef = Calendar.getInstance();
        bef.setTime(startTime);
        Calendar aft = Calendar.getInstance();
        aft.setTime(endTime);
        int result = aft.get(Calendar.MONTH) - bef.get(Calendar.MONTH);
        int month = (aft.get(Calendar.YEAR) - bef.get(Calendar.YEAR)) * 12;
        return month + result;
    }

    public static int getYear(Date startTime, Date endTime) {
        Calendar bef = Calendar.getInstance();
        bef.setTime(startTime);
        Calendar aft = Calendar.getInstance();
        aft.setTime(endTime);
        int year = (aft.get(Calendar.YEAR) - bef.get(Calendar.YEAR));
        return year;
    }

    /**
     * 时间 加减 月份
     */
    public static Date getBeforeDateMonth(Date date, int month) {
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        cl.set(Calendar.MONTH, cl.get(Calendar.MONTH) + month);
        return cl.getTime();
    }

    /**
     * 系统时间 加减 月份
     */
    public static Date getBeforeDateMonth(int month) {
        return getBeforeDateMonth(new Date(), month);
    }

    /**
     * 获取忽略毫秒的当前时间，用于与数据库中timestamp相对应
     *
     * @return
     * @Method：(用一句话描述该方法的实现功能)
     */
    public static Date getCurtimeIgnoreMs() {
        return new Date(System.currentTimeMillis() / 1000 * 1000);
    }

    /**
     * 日期加减操作工具类
     *
     * @param date
     * @param y
     * @param month
     * @param d
     * @param h
     * @param m
     * @param s
     * @return
     * @Method：()
     */
    public static Date dateChange(Date date, int y, int month, int d, int h, int m, int s) {
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(date);
        rightNow.add(Calendar.YEAR, y);
        rightNow.add(Calendar.MONTH, month);
        rightNow.add(Calendar.DAY_OF_YEAR, d);
        rightNow.add(Calendar.HOUR_OF_DAY, h);
        rightNow.add(Calendar.MINUTE, m);
        rightNow.add(Calendar.SECOND, s);
        return rightNow.getTime();
    }

    /**
     * Unix timestamp转本地Date类型
     *
     * @param timestamp
     * @return
     */
    public static Date timestamp2Date(long timestamp) {
        long dateTemp = Long.valueOf(timestamp);
        return new Date(dateTemp * 1000L);
    }

    /**
     * n为正数就是向后推n个自然周,n为负向前推n个自然周,默认星期一为一周的第一天
     *
     * @param n 当前周上n周还是下n周
     * @return
     */
    public static Date someWeekStartDate(int n) throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, n * 7);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        String dateNoHms = DateUtil.getFormatDate(cal.getTime(), DateUtil.DATE_FORMAT_1);
        Date date = DateUtil.getFormatDate(dateNoHms, DateUtil.DATE_FORMAT_1);
        return date;
    }

    /**
     * 获取任意月(当前月+-n)的第一天
     *
     * @param n (当前月+-n个个月)
     * @return
     */
    public static Date someMonthStartDate(int n) throws ParseException {
        Date date = DateUtil.dateChange(new Date(), 0, n, 0, 0, 0, 0);
        Date dateNoYmd = DateUtil.getFormatDate(DateUtil.getFormatDate(date, DateUtil.DATE_FORMAT_1), DateUtil.DATE_FORMAT_1);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateNoYmd);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        return calendar.getTime();
    }


    /**
     * 获取本季度开始时间
     *
     * @return
     */
    public static Date getCurrentQuarterStartTime() {
        Calendar c = Calendar.getInstance();
        int currentMonth = c.get(Calendar.MONTH) + 1;
        SimpleDateFormat longSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");
        Date now = null;
        try {
            if (currentMonth >= JAN && currentMonth <= MAR) {
                c.set(Calendar.MONTH, 0);
            } else if (currentMonth >= APR && currentMonth <= JUN) {
                c.set(Calendar.MONTH, 3);
            } else if (currentMonth >= JUL && currentMonth <= SEP) {
                c.set(Calendar.MONTH, 6);
            } else if (currentMonth >= OCT && currentMonth <= DEC) {
                c.set(Calendar.MONTH, 9);
            }
            c.set(Calendar.DATE, 1);
            now = longSdf.parse(shortSdf.format(c.getTime()) + " 00:00:00");
        } catch (Exception e) {
            LOGGER.error("获取本季度开始时间异常", e);
        }
        return now;
    }

    /**
     * 当前季度的结束时间
     *
     * @return
     */
    public static Date getCurrentQuarterEndTime() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getCurrentQuarterStartTime());
        cal.add(Calendar.MONTH, 3);
        return cal.getTime();
    }

    /**
     * 当前季度开始时间前后month个月
     *
     * @param month
     * @return
     */
    public static Date getCurrentQuarterEndTime(int month) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getCurrentQuarterStartTime());
        cal.add(Calendar.MONTH, month);
        return cal.getTime();
    }

    /**
     * 获取指定时间是今年的第几周
     *
     * @param date
     * @return
     */
    public static int getWeekNum(Date date) {
        Calendar cl = Calendar.getInstance();
        cl.setTime(date);
        return cl.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * 获取某个时间是第几周
     *
     * @param date
     * @return
     */
    public static int getISOWeekNum(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
    }

    /**
     * 获取指定时间年份的周数量
     *
     * @param date
     * @return
     */
    public static int getMaxWeekNumOfYear(Date date) {
        Calendar c = new GregorianCalendar();
        c.setTime(date);
        c.set(Calendar.MONTH, Calendar.DECEMBER);
        c.set(Calendar.DAY_OF_MONTH, 30);
        return getISOWeekNum(c.getTime());
    }

    /**
     * 获取某个给定日期的天,月,年,周第几天(周日算第一天),年第几天
     *
     * @param date
     * @return
     */
    public static Map<String, Object> getTimeMapNum(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int day = cal.get(Calendar.DATE);
        int month = cal.get(Calendar.MONTH) + 1;
        int year = cal.get(Calendar.YEAR);
        int dow = cal.get(Calendar.DAY_OF_WEEK);
        int dom = cal.get(Calendar.DAY_OF_MONTH);
        int doy = cal.get(Calendar.DAY_OF_YEAR);
        int quarter = 0;
        if (month >= JAN && month <= MAR) {
            quarter = 1;
        } else if (month >= APR && month <= JUN) {
            quarter = 2;
        } else if (month >= JUL && month <= SEP) {
            quarter = 3;
        } else if (month >= OCT && month <= DEC) {
            quarter = 4;
        }

        Map<String, Object> map = new HashMap<>(16);
        map.put("day", day);
        map.put("month", month);
        map.put("year", year);
        map.put("dayOfWeek", dow);
        map.put("dayOfMonty", dom);
        map.put("dayOfYear", doy);
        map.put("quarter", quarter);

        return map;
    }

    /**
     * 获取某一天的 h是m分s秒的Date类型
     *
     * @param date
     * @param h
     * @param m
     * @param s
     * @return
     */
    public static Date setSpecialTime(Date date, int h, int m, int s) {
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(date);
        rightNow.set(Calendar.HOUR_OF_DAY, h);
        rightNow.set(Calendar.MINUTE, m);
        rightNow.set(Calendar.SECOND, s);
        return rightNow.getTime();
    }
  /**
   * JDK8 String date ==> LocalDateTime
   * @param dateStr
   * @param format
   * @return
   */
  public static LocalDateTime stringDateToLocalDateTime(String dateStr){
    return stringDateToLocalDateTime(dateStr,DateUtil.DATE_FORMAT_2);
  }
    /**
     * JDK8 String date ==> LocalDateTime
     * @param dateStr
     * @param format
     * @return
     */
    public static LocalDateTime stringDateToLocalDateTime(String dateStr,String format){
        DateTimeFormatter dateTimeFormatter = getDateTimeFormatter(format);
        return LocalDateTime.parse(dateStr,dateTimeFormatter);
    }

    public static Date strToDate(String strDate) {
        return strToDate(strDate,"yyyy-MM-dd HH:mm:ss");
     }

    public static Date strToDate(String strDate,String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(strDate, pos);
        return strtodate;
     }
    /**
     * JDK8 LocalDate ==> Date
     *
     * @param localDate
     * @return
     */
    public static Date localDateToDate(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * JDK8 LocalDateTime ==> Date类型
     *
     * @param localDateTime
     * @return
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        return Date.from(zdt.toInstant());
    }

    /**
     * JDK8 LocalDate + LocalTime ==> Date
     *
     * @param localDate
     * @param localTime
     * @return
     */
    public static Date localTimeToDate(LocalDate localDate, LocalTime localTime) {
        LocalDateTime localDateTime = LocalDateTime.of(localDate, localTime);
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * JDK8 Date ==> LocalDate
     *
     * @param date
     * @return
     */
    public static LocalDate dateToLocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime.toLocalDate();
    }

    /**
     * JDK8 Date ==> LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /**
     * JDK8 Date ==> LocalTime
     *
     * @param date
     * @return
     */
    public static LocalTime dateToLocalTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime.toLocalTime();
    }
    /**
     * JDK8 LocalDateTime ==> String
     *
     * @param localDateTime
     * @param format
     * @return
     */
    public static String dateTimeFormat(Date date, String format) {
      SimpleDateFormat fmt = new SimpleDateFormat(format);
      return fmt.format(date);
    }
    /**
     * JDK8 LocalDateTime ==> String
     *
     * @param localDateTime
     * @param format
     * @return
     */
    public static String localDateTimeFormat(LocalDateTime localDateTime) {
        return localDateTimeFormat(localDateTime,DateUtil.DATE_FORMAT_2);
    }
    /**
     * JDK8 LocalDateTime ==> String
     *
     * @param localDateTime
     * @param format
     * @return
     */
    public static String localDateTimeFormat(LocalDateTime localDateTime, String format) {
        return localDateTime.format(getDateTimeFormatter(format));
    }

    /**
     * JDK8 LocalDate ==> String 没有时分秒自动补0
     * @param localDate
     * @param format
     * @return
     */
    public static String localDateFormat(LocalDate localDate,String format){
        return localDate.format(getDateTimeFormatter(format));
    }

    /**
     * JDK8 LocalTime ==> String
     * @param localTime
     * @param format
     * @return
     */
    public static String localTimeFormat(LocalTime localTime,String format){
        return localTime.format(getDateTimeFormatter(format));
    }

    private static DateTimeFormatter getDateTimeFormatter(String format) {
        return DateTimeFormatter.ofPattern(format);
    }

    /**
     * unix时间转long时间戳
     * @param unixTime
     * @return
     */
    public static Long convertTime(String unixTime){
        if (StringUtils.isNotBlank(unixTime)){
            return new Double(Double.parseDouble(unixTime)).longValue();
        }
        return LocalDateTime.now().toEpochSecond(OffsetDateTime.now().getOffset());
    }

    public static long getTimestampOfLocalDateTime(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return instant.toEpochMilli();
    }

    /**
     * 时间字符串转long时间戳
     * @param unixTime
     * @return
     */
    public static Long getTimeByString(String unixTime, String format){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        Date date = null;
        try {
            date = simpleDateFormat.parse(unixTime);
        } catch (ParseException e) {
            LOGGER.error("字符串to时间戳异常", e);
        }
        return date.getTime();
    }

    /**
     * 两个时间差多少天,多少小时,多少分钟,多少秒
     * @param start
     * @param end
     * @return
     */
    public static String timeInterval(LocalDateTime start,LocalDateTime end){
        long startTimestamp = start.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        long endTimestamp = end.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        long ns = 1000;
        StringBuilder sb = new StringBuilder();
        long diff = endTimestamp - startTimestamp;
        long day = diff / nd;
        if (day > 0) {
            sb.append(day + "天");
        }
        long hour = diff % nd / nh;
        if (hour > 0 || day > 0) {
            sb.append(hour + "小时");
        }
        long minute = diff % nd % nh / nm;
        if (minute > 0 || hour > 0 || day > 0) {
            sb.append(minute + "分钟");
        }
        long second = diff % nd % nh % nm / ns;
        if (second > 0 || minute > 0 || hour > 0 || day > 0) {
            sb.append(second + "秒");
        }
        return sb.toString();
    }


    /**
     * 把日期字符串格式化成日期类型
     * @param dateStr
     * @param format
     * @return
     */
    public static Date convert2Date(String dateStr, String format) {
        SimpleDateFormat simple = new SimpleDateFormat(format);
        try {
            simple.setLenient(false);
            return simple.parse(dateStr);
        } catch (Exception e) {
            e.printStackTrace();
            return  null;
        }
    }


    /**
     * 把日期类型格式化成字符串
     * @param date
     * @param format
     * @return
     */
    public static String convert2String(Date date, String format) {
        SimpleDateFormat formater = new SimpleDateFormat(format);
        try {
            return formater.format(date);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 转sql的time格式
     * @param date
     * @return
     */
    public static java.sql.Timestamp convertSqlTime(Date date){
        java.sql.Timestamp timestamp = new java.sql.Timestamp(date.getTime());
        return timestamp;
    }

    /**
     * 转sql的日期格式
     * @param date
     * @return
     */
    public static java.sql.Date convertSqlDate(Date date){
        java.sql.Date datetamp = new java.sql.Date(date.getTime());
        return datetamp;
    }


    /**
     * 获取当前日期
     * @param format
     * @return
     */
    public static String getCurrentDate(String format) {
        return new SimpleDateFormat(format).format(new Date());
    }

    /**
     * 获取当前日期
     * @date 2018/9/13 下午3:36
     * @return java.util.Date
     */
    public static Date getCurrentDate() {
        return new Date();
    }

    /**
     * 获取时间戳
     * @return
     */
    public static long getTimestamp()
    {
        return System.currentTimeMillis();
    }

    /**
     * 获取月份的天数
     * @param year
     * @param month
     * @return
     */
    public static int getDaysOfMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, 1);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取日期的年
     * @param date
     * @return
     */
    public static int getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 获取日期的月
     * @param date
     * @return
     */
    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 获取日期的日
     * @param date
     * @return
     */
    public static int getDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DATE);
    }

    /**
     * 获取日期的时
     * @param date
     * @return
     */
    public static int getHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.HOUR);
    }

    /**
     * 获取日期的分种
     * @param date
     * @return
     */
    public static int getMinute(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MINUTE);
    }

    /**
     * 获取日期的秒
     * @param date
     * @return
     */
    public static int getSecond(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.SECOND);
    }

    /**
     * 获取星期几
     * @param date
     * @return
     */
    public static int getWeekDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        return dayOfWeek-1;
    }

    /**
     * 获取哪一年共有多少周
     * @param year
     * @return
     */
    public static int getMaxWeekNumOfYear(int year) {
        Calendar c = new GregorianCalendar();
        c.set(year, Calendar.DECEMBER, 31, 23, 59, 59);
        return getWeekNumOfYear(c.getTime());
    }

    /**
     * 取得某天是一年中的多少周
     * @param date
     * @return
     */
    public static int getWeekNumOfYear(Date date) {
        Calendar c = new GregorianCalendar();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setMinimalDaysInFirstWeek(7);
        c.setTime(date);
        return c.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * 取得某天所在周的第一天
     * @param date
     * @return
     */
    public static Date getFirstDayOfWeek(Date date) {
        Calendar c = new GregorianCalendar();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setTime(date);
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek());
        return c.getTime();
    }

    /**
     * 取得某天所在周的最后一天
     * @param date
     * @return
     */
    public static Date getLastDayOfWeek(Date date) {
        Calendar c = new GregorianCalendar();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setTime(date);
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek() + 6);
        return c.getTime();
    }

    /**
     * 取得某年某周的第一天 对于交叉:2008-12-29到2009-01-04属于2008年的最后一周,2009-01-05为2009年第一周的第一天
     * @param year
     * @param week
     * @return
     */
    public static Date getFirstDayOfWeek(int year, int week) {
        Calendar calFirst = Calendar.getInstance();
        calFirst.set(year, 0, 7);
        Date firstDate = getFirstDayOfWeek(calFirst.getTime());

        Calendar firstDateCal = Calendar.getInstance();
        firstDateCal.setTime(firstDate);

        Calendar c = new GregorianCalendar();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, Calendar.JANUARY);
        c.set(Calendar.DATE, firstDateCal.get(Calendar.DATE));

        Calendar cal = (GregorianCalendar) c.clone();
        cal.add(Calendar.DATE, (week - 1) * 7);
        firstDate = getFirstDayOfWeek(cal.getTime());

        return firstDate;
    }

    /**
     * 取得某年某周的最后一天 对于交叉:2008-12-29到2009-01-04属于2008年的最后一周, 2009-01-04为
     * 2008年最后一周的最后一天
     * @param year
     * @param week
     * @return
     */
    public static Date getLastDayOfWeek(int year, int week) {
        Calendar calLast = Calendar.getInstance();
        calLast.set(year, 0, 7);
        Date firstDate = getLastDayOfWeek(calLast.getTime());

        Calendar firstDateCal = Calendar.getInstance();
        firstDateCal.setTime(firstDate);

        Calendar c = new GregorianCalendar();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, Calendar.JANUARY);
        c.set(Calendar.DATE, firstDateCal.get(Calendar.DATE));

        Calendar cal = (GregorianCalendar) c.clone();
        cal.add(Calendar.DATE, (week - 1) * 7);
        Date lastDate = getLastDayOfWeek(cal.getTime());

        return lastDate;
    }


    private static Date add(Date date, int calendarField, int amount) {
        if (date == null) {
            throw new IllegalArgumentException("The date must not be null");
        } else {
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.add(calendarField, amount);
            return c.getTime();
        }
    }

    /*
     * 1则代表的是对年份操作， 2是对月份操作， 3是对星期操作， 5是对日期操作， 11是对小时操作， 12是对分钟操作， 13是对秒操作，
     * 14是对毫秒操作
     */

    /**
     * 增加年
     * @param date
     * @param amount
     * @return
     */
    public static Date addYears(Date date, int amount) {
        return add(date, 1, amount);
    }

    /**
     * 增加月
     * @param date
     * @param amount
     * @return
     */
    public static Date addMonths(Date date, int amount) {
        return add(date, 2, amount);
    }

    /**
     * 增加周
     * @param date
     * @param amount
     * @return
     */
    public static Date addWeeks(Date date, int amount) {
        return add(date, 3, amount);
    }

    /**
     * 增加天
     * @param date
     * @param amount
     * @return
     */
    public static Date addDays(Date date, int amount) {
        return add(date, 5, amount);
    }

    /**
     * 增加时
     * @param date
     * @param amount
     * @return
     */
    public static Date addHours(Date date, int amount) {
        return add(date, 11, amount);
    }

    /**
     * 增加分
     * @param date
     * @param amount
     * @return
     */
    public static Date addMinutes(Date date, int amount) {
        return add(date, 12, amount);
    }

    /**
     * 增加秒
     * @param date
     * @param amount
     * @return
     */
    public static Date addSeconds(Date date, int amount) {
        return add(date, 13, amount);
    }

    /**
     * 增加毫秒
     * @param date
     * @param amount
     * @return
     */
    public static Date addMilliseconds(Date date, int amount) {
        return add(date, 14, amount);
    }



    /**
     * time差
     * @param before
     * @param after
     * @return
     */
    public static long diffTimes(Date before, Date after){
        return after.getTime() - before.getTime();
    }

    /**
     * 秒差
     * @param before
     * @param after
     * @return
     */
    public static long diffSecond(Date before, Date after){
        return (after.getTime() - before.getTime())/1000;
    }

    /**
     * 分种差
     * @param before
     * @param after
     * @return
     */
    public static int diffMinute(Date before, Date after){
        return (int)((after.getTime() - before.getTime())/1000/60);
    }

    /**
     * 时差
     * @param before
     * @param after
     * @return
     */
    public static int diffHour(Date before, Date after){
        return (int)(after.getTime() - before.getTime())/1000/60/60;
    }

    /**
     * 天数差
     * @param before
     * @param after
     * @return
     */
    public static int diffDay(Date before, Date after) {
        return Integer.parseInt(String.valueOf(((after.getTime() - before.getTime()) / 86400000)));
    }

    /**
     * 月差
     * @param before
     * @param after
     * @return
     */
    public static int diffMonth(Date before, Date after){
        int monthAll=0;
        int yearsX = diffYear(before,after);
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(before);
        c2.setTime(after);
        int monthsX = c2.get(Calendar.MONTH) - c1.get(Calendar.MONTH);
        monthAll=yearsX*12+monthsX;
        int daysX =c2.get(Calendar.DATE) - c1.get(Calendar.DATE);
        if(daysX>0){
            monthAll=monthAll+1;
        }
        return monthAll;
    }

    /**
     * 年差
     * @param before
     * @param after
     * @return
     */
    public static int diffYear(Date before, Date after) {
        return getYear(after) - getYear(before);
    }

    /**
     * 设置00:00:00
     * @param date
     * @return
     */
    public static Date setStartDay(Date date) {
        String d = convert2String(date, "yyyy-MM-dd") + " 00:00:00";
        return DateUtil.convert2Date(d, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 设置23:59:59
     * @param date
     * @return
     */
    public static Date setEndDay(Date date) {
        String d = convert2String(date, "yyyy-MM-dd") + " 23:59:59";
        return DateUtil.convert2Date(d, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 获取指定日期
     * @return
     */
    public static String getDaysAgo(Integer day) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        calendar.add(Calendar.DATE, day);
        String daysAgo = sdf.format(calendar.getTime());
        return daysAgo;
    }
    /**
     * 两个时间差多少天,多少小时,多少分钟,多少秒
     * @param start
     * @param end
     * @return
     */
    public static String timeInterval(Date start,Date end){
        long startTimestamp = start.getTime();
        long endTimestamp = end.getTime();
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        long ns = 1000;
        StringBuilder sb = new StringBuilder();
        long diff = endTimestamp - startTimestamp;
        long day = diff / nd;
        if (day > 0) {
            sb.append(day + "天");
        }
        long hour = diff % nd / nh;
        if (hour > 0 || day > 0) {
            sb.append(hour + "小时");
        }
        long minute = diff % nd % nh / nm;
        if (minute > 0 || hour > 0 || day > 0) {
            sb.append(minute + "分钟");
        }
        long second = diff % nd % nh % nm / ns;
        if (second > 0 || minute > 0 || hour > 0 || day > 0) {
            sb.append(second + "秒");
        }
        return sb.toString();
    }

    /**
     * 获取当前时间字符串，默认"yyyy-MM-dd HH:mm:ss"
     * @return
     */
    public static String getCurrentTimeString() {
        SimpleDateFormat dateformat = getSimpleDateFormat(DATE_FORMAT_2);
        return dateformat.format(new Date());
    }
}

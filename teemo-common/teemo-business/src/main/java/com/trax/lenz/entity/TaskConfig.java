package com.trax.lenz.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 任务配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String taskId;

    private String taskName;

    private Integer type;

    private String submitMethod;

    private String queryMethod;

    private String callbackMethod;

    private Integer callbackEnable;

    private Integer callbackType;

    private String callbackUrl;

    private Integer callbackMaxNum;

    private Date createTime;

    private Date updateTime;

}

package com.trax.lenz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 雪花贵州POC原始结果记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SnowbeerResultDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 拜访单据号
     */
    private String visitCode;

    /**
     * 答卷id
     */
    private String responseId;

    /**
     * 拜访日期
     */
    private String visitDate;

    /**
     * 营销中心
     */
    private String marketingCenter;

    /**
     * 销售大区
     */
    private String salesRegion;

    /**
     * 业务部
     */
    private String businessSegment;

    /**
     * 工作站
     */
    private String workstation;

    /**
     * 业务线
     */
    private String serviceLine;

    /**
     * 照片链接
     */
    private String imageUrl;

    /**
     * 端一级类型
     */
    private String terminalType;

    /**
     * 点管理分级
     */
    private String manageScale;

    /**
     * 点档次分级
     */
    private String manageGrade;

    /**
     * 连锁属性
     */
    private String chainProperty;

    /**
     * 连锁公司
     */
    private String chainCorporation;

    /**
     * 连锁品牌
     */
    private String chainBrand;

    /**
     * 终端状态
     */
    private String terminalState;

    /**
     * 终端编码
     */
    private String terminalCoding;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 级照片类型
     */
    private String firstImgType;

    /**
     * 级照片类型
     */
    private String secondImgType;

    /**
     * 级照片类型
     */
    private String thirdImgType;

    /**
     * 拜访类型
     */
    private String visitType;

    /**
     * 集人员编码
     */
    private String personCode;

    /**
     * 集人员姓名
     */
    private String personName;

    /**
     * 终端业务员编码
     */
    private String agentCode;

    /**
     * 终端业务员姓名
     */
    private String agentName;

    /**
     * 客户图片Id
     */
    private String customerImageId;

    /**
     * 是否翻拍 0:否 1:是
     */
    private Integer remake;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_1")
    private Integer extend1;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_2")
    private Integer extend2;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_3")
    private Integer extend3;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_4")
    private Integer extend4;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_5")
    private Integer extend5;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_6")
    private Integer extend6;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_7")
    private Integer extend7;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_8")
    private Integer extend8;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_9")
    private Integer extend9;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_10")
    private Integer extend10;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_11")
    private Integer extend11;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_12")
    private Integer extend12;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_13")
    private Integer extend13;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_14")
    private Integer extend14;

    /**
     * 0:否 1:是
     */
    @TableField(value = "extend_15")
    private Integer extend15;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}

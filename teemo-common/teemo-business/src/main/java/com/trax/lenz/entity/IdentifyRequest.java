package com.trax.lenz.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 客户识别请求记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IdentifyRequest extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private String responseId;

    private String customerRequestId;

    private String taskId;

    private String taskName;

    private Integer status;

    private Date createTime;

    private Integer test;

    private String reqContent;

    private String apiReqContent;

    private Date identifyFinishTime;

    private String apiRespContent;

    private Integer callbackStatus;

    private Integer counts;

    private Integer retryEnabled;

    private String callbackUrl;

    private String callbackType;

    private String callbackParam;

    private String callbackHttpCode;

    private String callbackResponse;

    /**
     * 是否有子任务
     */
    private Integer type;

    private Date updateTime;

}

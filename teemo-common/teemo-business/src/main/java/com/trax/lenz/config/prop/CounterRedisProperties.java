package com.trax.lenz.config.prop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 计数器 Redis 配置文件
 *
 * <AUTHOR>
 * @date 2024-04-28
 */
@Data
@ConfigurationProperties(prefix = "spring.redis-counter")
@Component
public class CounterRedisProperties {

    private String host;

    private Integer port;

    private String username;

    private String password;

    private Integer database;
    
    private Pool pool = new Pool();
    
    @Data
    public static class Pool {
        private Integer maxActive;
        private Integer maxWait;
        private Integer maxIdle;
        private Integer minIdle;
    }
} 
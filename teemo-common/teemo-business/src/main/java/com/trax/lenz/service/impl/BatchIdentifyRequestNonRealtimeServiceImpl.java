package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trax.lenz.entity.BatchIdentifyRequest;
import com.trax.lenz.entity.BatchIdentifyRequestNonRealtime;
import com.trax.lenz.entity.PgBatchAppealRequestNonRealtime;
import com.trax.lenz.mapper.BatchIdentifyRequestNonRealtimeMapper;
import com.trax.lenz.service.IBatchIdentifyRequestNonRealtimeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 客户批量计算请求记录表(非实时) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-11-06
 */
@Service
public class BatchIdentifyRequestNonRealtimeServiceImpl extends ServiceImpl<BatchIdentifyRequestNonRealtimeMapper, BatchIdentifyRequestNonRealtime> implements IBatchIdentifyRequestNonRealtimeService {

    public BatchIdentifyRequestNonRealtime getOne(String responseGroupId) {
        LambdaQueryWrapper<BatchIdentifyRequestNonRealtime> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BatchIdentifyRequestNonRealtime::getResponseGroupId, responseGroupId);
        return getOne(wrapper);
    }

    public void update(String responseGroupId, BatchIdentifyRequestNonRealtime batchIdentifyRequest) {
        LambdaQueryWrapper<BatchIdentifyRequestNonRealtime> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BatchIdentifyRequestNonRealtime::getResponseGroupId, responseGroupId);
        update(batchIdentifyRequest, wrapper);
    }
}

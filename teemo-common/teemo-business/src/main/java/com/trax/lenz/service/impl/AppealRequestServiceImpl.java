package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trax.lenz.entity.AppealRequest;
import com.trax.lenz.entity.IdentifyRequest;
import com.trax.lenz.mapper.AppealRequestMapper;
import com.trax.lenz.service.AppealRequestService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 客户申诉请求记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Service
public class AppealRequestServiceImpl extends ServiceImpl<AppealRequestMapper, AppealRequest> implements AppealRequestService {

    public AppealRequest getOne(String appealId) {
        LambdaQueryWrapper<AppealRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppealRequest::getAppealId, appealId);
        return getOne(wrapper);
    }

    public AppealRequest getOneByAppealType(String responseId, Integer appealType) {
        LambdaQueryWrapper<AppealRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppealRequest::getResponseId, responseId);
        wrapper.eq(AppealRequest::getType, appealType);
        return getOne(wrapper);
    }

    public AppealRequest getLastAppealReq(String responseId) {
        LambdaQueryWrapper<AppealRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppealRequest::getResponseId, responseId);
        wrapper.orderByDesc(AppealRequest::getCreateTime);
        wrapper.last("limit 1");
        return getOne(wrapper);
    }

    public void update(String appealId, AppealRequest appealRequest){
        LambdaQueryWrapper<AppealRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppealRequest::getAppealId, appealId);
        update(appealRequest, wrapper);
    }

    public boolean remove(String responseId) {
        LambdaQueryWrapper<AppealRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppealRequest::getResponseId, responseId);
        return remove(wrapper);
    }
}

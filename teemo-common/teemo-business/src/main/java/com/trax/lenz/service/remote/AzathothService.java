package com.trax.lenz.service.remote;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.trax.lenz.azathoth.dto.common.base.product.RemoteProductDTO;
import com.trax.lenz.azathoth.dto.request.base.prdocut.RemoteGetProductListReq;
import com.trax.lenz.azathoth.dto.response.base.product.RemoteGetProductInfoListResp;
import com.trax.lenz.common.core.domain.R;
import com.trax.lenz.common.utils.HttpUtil;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.dto.HttpResponse;
import com.trax.lenz.mario.request.product.ProductEntityListQueryReq;
import com.trax.lenz.mario.response.product.ProductEntitySimpleResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 调用azathoth服务接口Service
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
@Slf4j
@Service
public class AzathothService {

    private volatile Map<Long, RemoteProductDTO> skuMap = new HashMap<>();

    @Value("${azathoth.getProductList:http://gateway-service.langjtech.com/azathoth/base/product/getProductList}")
    private String getProductListUrl;

    @Value("${mario.getListByIdList:http://gateway-service.langjtech.com/mario/productEntity/getListByIdList}")
    private String getListByIdList;

    /**
     * 清空内存（10分钟）
     */
    @Scheduled(fixedRate = 10 * 60 * 1000)
    public void clearMap() {
        log.info("每10分钟清空azathoth本地缓存!");
        skuMap.clear();
    }

    /**
     * 查询商品信息
     *
     * @param skuIdList
     * @return String
     */
    public List<RemoteProductDTO> getSkuDetailList(List<Long> skuIdList) {
        long startTime = System.currentTimeMillis();
        if (skuIdList.isEmpty()) {
            return Lists.newArrayList();
        }
        List<RemoteProductDTO> remoteProductDTOList = Lists.newArrayList();
        List<Long> newSkuIdList = Lists.newArrayList();
        for (Long skuId : skuIdList) {
            if (skuMap.containsKey(skuId)) {
                remoteProductDTOList.add(skuMap.get(skuId));
            } else {
                newSkuIdList.add(skuId);
            }
        }
        if (CollectionUtils.isEmpty(newSkuIdList)) {
            return remoteProductDTOList;
        }
        try {
            RemoteGetProductListReq req = new RemoteGetProductListReq();
            req.setProductIdList(newSkuIdList);
            HttpResponse post = HttpUtil.post(getProductListUrl, JsonUtil.toJsonString(req), null);
            R<RemoteGetProductInfoListResp> respR = JsonUtil.parseObject(post.getResponse(), new TypeReference<R<RemoteGetProductInfoListResp>>() {
            });
            log.info("rpc-azathoth服务-查询sku信息！skuIdList={}, returnJson={}, 耗时:{}", newSkuIdList, JsonUtil.toJsonString(respR), System.currentTimeMillis() - startTime);
            if (respR.isOk()) {
                List<RemoteProductDTO> productList = respR.getData().getProductList();
                for (RemoteProductDTO remoteProductDTO : productList) {
                    if (remoteProductDTO.getProductId() != null) {
                        skuMap.put(remoteProductDTO.getProductId(), remoteProductDTO);
                    }
                    if (remoteProductDTO.getTreeId() != null) {
                        skuMap.put(remoteProductDTO.getTreeId(), remoteProductDTO);
                    }
                    remoteProductDTOList.add(remoteProductDTO);
                }
                return remoteProductDTOList;
            }
        } catch (Exception e) {
            log.error("rpc-azathoth服务-查询sku信息异常！skuIdList={}", skuIdList, e);
        }
        return Lists.newArrayList();
    }

    /**
     * 查询商品信息Map
     *
     * @param skuIdList
     * @return Map<Long, RemoteProductDTO>
     */
    public Map<Long, RemoteProductDTO> getProductMap(List<Long> skuIdList, Long responseType) {
        // 商品库
        Map<Long, RemoteProductDTO> productMap = Maps.newHashMap();
        if (responseType == null || 0 == responseType) {
            List<RemoteProductDTO> skuDetailList = getSkuDetailList(skuIdList);
            for (RemoteProductDTO dto : skuDetailList) {
                if (dto.getTreeId() != null) {
                    productMap.put(dto.getTreeId(), dto);
                }
                if (dto.getProductId() != null) {
                    productMap.put(dto.getProductId(), dto);
                }
            }
        } else {
            // 新商品库
            ProductEntityListQueryReq productEntityListQueryReq = new ProductEntityListQueryReq();
            productEntityListQueryReq.setEntityIdList(skuIdList);
            HttpResponse post = HttpUtil.post(getListByIdList, JsonUtil.toJsonString(productEntityListQueryReq), null);
            R<List<ProductEntitySimpleResp>> listByIdList = JsonUtil.parseObject(post.getResponse(), new TypeReference<R<List<ProductEntitySimpleResp>>>() {
            });
            List<ProductEntitySimpleResp> dataList = listByIdList.getData();
            productMap = dataList.stream().collect(Collectors.toMap(ProductEntitySimpleResp::getPackageId, item -> {
                RemoteProductDTO dto = new RemoteProductDTO();
                dto.setProductId(item.getPackageId());
                dto.setCnName(item.getPackageName());
                dto.setEnName(null);
                return dto;
            }));
        }
        return productMap;
    }
}


package com.trax.lenz.config.prop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName CustomConfigProperties
 * @Description:
 * <AUTHOR>
 * @Date 2024-05-08-17:55
 **/
@Data
@ConfigurationProperties(prefix = "custom.config")
@Component
public class CustomConfigProperties {

    private SnowBeer snowbeer;

    private NewPpz newPpz;

    @Data
    public static class SnowBeer {
        private String submitUrl;
        private String intervalTime;
    }

    @Data
    public static class NewPpz {
        private String customerId;
        private String submitMethod;
        private String queryMethod;
        private String callbackMethod;
        private String callbackUrl;
    }

}

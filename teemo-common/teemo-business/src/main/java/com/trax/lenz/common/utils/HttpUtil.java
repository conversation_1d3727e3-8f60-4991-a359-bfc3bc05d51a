package com.trax.lenz.common.utils;

import com.trax.lenz.dto.HttpResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class HttpUtil {

    final static int BUFFER_SIZE = 4096;

    /**
     * get
     *
     * @param url
     * @return
     */
    public static HttpResponse get(String url) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpResponse httpResponse = new HttpResponse();
        HttpEntity entity = null;
        try {
            log.info("url = " + url);
            HttpGet httpGet = new HttpGet(url);
            CloseableHttpResponse response = httpclient.execute(httpGet);
            log.info("http response code = " + response.getStatusLine().getStatusCode());
            httpResponse.setCode(String.valueOf(response.getStatusLine().getStatusCode()));
            entity = response.getEntity();
            String content = HttpUtil.inputStreamTOString(entity.getContent());
            httpResponse.setResponse(content);
            log.info("result = " + content);
        } catch (Exception e) {
            log.error("http get error!", e);
        } finally {
            if (entity != null) {
                try {
                    EntityUtils.consume(entity);
                } catch (IOException e) {
                    log.error("http get entity close error!", e);
                }
            }
        }
        return httpResponse;
    }

    /**
     * get
     *
     * @param url
     * @return
     */
    public static HttpResponse put(String url) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpResponse httpResponse = new HttpResponse();
        HttpEntity entity = null;
        try {
            log.info("url = " + url);
            HttpPut httpGet = new HttpPut(url);
            CloseableHttpResponse response = httpclient.execute(httpGet);
            log.info("http response code = " + response.getStatusLine().getStatusCode());
            httpResponse.setCode(String.valueOf(response.getStatusLine().getStatusCode()));
            entity = response.getEntity();
            String content = HttpUtil.inputStreamTOString(entity.getContent());
            httpResponse.setResponse(content);
            log.info("result = " + content);
        } catch (Exception e) {
            log.error("http get error!", e);
        } finally {
            if (entity != null) {
                try {
                    EntityUtils.consume(entity);
                } catch (IOException e) {
                    log.error("http get entity close error!", e);
                }
            }
        }
        return httpResponse;
    }

    private static String inputStreamTOString(InputStream in) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] data = new byte[BUFFER_SIZE];
        int count = -1;
        while ((count = in.read(data, 0, BUFFER_SIZE)) != -1) {
            outStream.write(data, 0, count);
        }
        data = null;
        return new String(outStream.toByteArray(), "UTF-8");
    }

    /**
     * post
     *
     * @param url
     * @param json
     * @param headerMap
     * @return
     */
    public static HttpResponse post(String url, String json, Map<String, String> headerMap) {
        CloseableHttpClient client = HttpClients.createDefault();
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(120000).setConnectionRequestTimeout(120000)
                .setSocketTimeout(120000).build();
        HttpResponse httpResponse = new HttpResponse();
        CloseableHttpResponse response = null;
        StringEntity entity = null;
        try {
            //建立Request的对象，一般用目标url来构造，Request一般配置addHeader、setEntity、setConfig
            HttpPost req = new HttpPost(url);
            req.setConfig(requestConfig);
            if (headerMap != null && !headerMap.isEmpty()) {
                for (String headKey : headerMap.keySet()) {
                    req.addHeader(headKey, headerMap.get(headKey));
                }
            }
            entity = new StringEntity(json, "utf-8");
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            req.setEntity(entity);
            log.info("url = " + url);
            log.info("json = " + json);
            long start = System.currentTimeMillis();
            response = client.execute(req);
            String result = EntityUtils.toString(response.getEntity(), "UTF-8");
            log.info("http response code = " + response.getStatusLine().getStatusCode());
            httpResponse.setCode(String.valueOf(response.getStatusLine().getStatusCode()));
            log.info("result = " + result);
//            logger.info("time = " + (System.currentTimeMillis() - start) / 1000.0);
            httpResponse.setResponse(result);
        } catch (Exception e) {
            log.error("post http error!", e);
        } finally {
            try {
                EntityUtils.consume(entity);
            } catch (Exception e) {
                log.error("post http error!", e);
            }
        }
        return httpResponse;
    }

    /**
     * postForMap
     *
     * @param url
     * @param paramMap
     * @return
     */
    public static HttpResponse postForMap(String url, Map<String, String> paramMap, Map<String, String> headerMap) {
        CloseableHttpClient client = HttpClients.createDefault();
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(120000).setConnectionRequestTimeout(120000)
                .setSocketTimeout(120000).build();
        HttpResponse httpResponse = new HttpResponse();
        CloseableHttpResponse response = null;
        UrlEncodedFormEntity entity = null;
        try {
            //建立Request的对象，一般用目标url来构造，Request一般配置addHeader、setEntity、setConfig
            HttpPost req = new HttpPost(url);
            req.setConfig(requestConfig);
            if (headerMap != null && !headerMap.isEmpty()) {
                for (String headKey : headerMap.keySet()) {
                    req.addHeader(headKey, headerMap.get(headKey));
                }
            }
            List<NameValuePair> nameValuePairArrayList = new ArrayList<NameValuePair>();
            for (String paramKey : paramMap.keySet()) {
                nameValuePairArrayList.add(new BasicNameValuePair(paramKey, paramMap.get(paramKey)));
            }
            entity = new UrlEncodedFormEntity(nameValuePairArrayList, "utf-8");
            req.setEntity(entity);
            log.info("url = " + url);
            response = client.execute(req);
            String result = EntityUtils.toString(response.getEntity(), "UTF-8");
            log.info("http response code = " + response.getStatusLine().getStatusCode());
            httpResponse.setCode(String.valueOf(response.getStatusLine().getStatusCode()));
            log.info("result = " + result);
            httpResponse.setResponse(result);
        } catch (Exception e) {
            log.error("post http error!", e);
        } finally {
            try {
                EntityUtils.consume(entity);
            } catch (Exception e) {
                log.error("post http error!", e);
            }
        }
        return httpResponse;
    }
}

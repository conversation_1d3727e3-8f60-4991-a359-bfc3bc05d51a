package com.trax.lenz.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 统一客户查重请求记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PecRepeatRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String checkRepeatId;

    /**
     * 唯一请求id
     */
    private String customerRequestId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 状态 1.待处理 2.处理中 3.处理完成
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 测试 0.否 1.是
     */
    private Integer test;

    /**
     * 请求原始内容
     */
    private String reqContent;

    /**
     * 回调状态 （1.成功 2.失败 3.暂停重试）
     */
    private Integer callbackStatus;

    /**
     * 累计回调次数
     */
    private Integer counts;

    /**
     * 是否需要重新推送（0.否 1.是）
     */
    private Integer retryEnabled;

    /**
     * 请求地址
     */
    private String callbackUrl;

    /**
     * 请求类型
     */
    private String callbackType;

    /**
     * 回调内容
     */
    private String callbackParam;

    /**
     * http响应码
     */
    private String callbackHttpCode;

    /**
     * http响应内容
     */
    private String callbackResponse;

    /**
     * 更新时间
     */
    private Date updateTime;


}

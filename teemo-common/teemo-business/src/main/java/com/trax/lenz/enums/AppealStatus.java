package com.trax.lenz.enums;

import lombok.Getter;

/**
 * AppealStatus
 *
 * @author: y<PERSON><PERSON>
 * @date: 2023-03-07
 */
@Getter
public enum AppealStatus {

    INIT(1, "待处理"),

    APPEAL_ING(2, "申诉中"),

    APPEAL_FINISH(3, "申诉完成");

    private final Integer code;

    private final String desc;

    AppealStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}

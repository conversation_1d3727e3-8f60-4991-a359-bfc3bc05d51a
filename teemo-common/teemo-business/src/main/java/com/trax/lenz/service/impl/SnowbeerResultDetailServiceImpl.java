package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trax.lenz.entity.SnowbeerResultDetail;
import com.trax.lenz.mapper.SnowbeerResultDetailMapper;
import com.trax.lenz.service.ISnowbeerResultDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 雪花贵州POC原始结果记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Service
public class SnowbeerResultDetailServiceImpl extends ServiceImpl<SnowbeerResultDetailMapper, SnowbeerResultDetail> implements ISnowbeerResultDetailService {

    public void updateByVisitCode(String visitCode, SnowbeerResultDetail detail) {
        LambdaQueryWrapper<SnowbeerResultDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SnowbeerResultDetail::getVisitCode, visitCode);
        update(detail, wrapper);
    }


    @Override
    public void batchInsert(List<SnowbeerResultDetail> snowbeerResultDetailList) {
        baseMapper.batchInsert(snowbeerResultDetailList);
    }
}

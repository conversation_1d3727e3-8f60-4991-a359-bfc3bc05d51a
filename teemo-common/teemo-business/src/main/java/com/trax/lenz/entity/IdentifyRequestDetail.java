package com.trax.lenz.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 识别请求记录子能力明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class IdentifyRequestDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String detailId;

    /**
     * 父级Id
     */
    private String responseId;

    /**
     * 状态 1.待处理 2.处理中 3.处理完成
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 测试 0.否 1.是
     */
    private Integer test;

    /**
     * 能力类型 1:翻拍 2:查重...
     */
    private Integer type;

    /**
     * 子能力执行Id
     */
    private String childId;

    /**
     * 子能力平台请求内容
     */
    private String childReqContent;

    /**
     * 子能力平台处理完成时间
     */
    private Date finishTime;

    /**
     * 子能力平台响应内容
     */
    private String childRespContent;

    private Integer imageNum;

    /**
     * 拓展信息01
     */
    @TableField(value = "extend_1")
    private String extend1;

    /**
     * 拓展信息02
     */
    @TableField(value = "extend_1")
    private String extend2;

    /**
     * 拓展信息03
     */
    @TableField(value = "extend_1")
    private String extend3;

    /**
     * 拓展信息04
     */
    @TableField(value = "extend_4")
    private String extend4;

    /**
     * 拓展信息05
     */
    @TableField(value = "extend_5")
    private String extend5;

    /**
     * 拓展信息06
     */
    @TableField(value = "extend_6")
    private String extend6;

    /**
     * 拓展信息07
     */
    @TableField(value = "extend_7")
    private String extend7;

    /**
     * 拓展信息08
     */
    @TableField(value = "extend_8")
    private String extend8;

    /**
     * 拓展信息09
     */
    @TableField(value = "extend_9")
    private String extend9;

    /**
     * 拓展信息10
     */
    @TableField(value = "extend_10")
    private String extend10;

    /**
     * 回调状态 （1.成功 2.失败 3.暂停重试）
     */
    private Integer callbackStatus;

    /**
     * 累计回调次数
     */
    private Integer counts;

    /**
     * 是否需要重新推送（0.否 1.是）
     */
    private Integer retryEnabled;

    /**
     * 请求地址
     */
    private String callbackUrl;

    /**
     * 请求类型
     */
    private String callbackType;

    /**
     * 回调内容
     */
    private String callbackParam;

    /**
     * http响应码
     */
    private String callbackHttpCode;

    /**
     * http响应内容
     */
    private String callbackResponse;

    /**
     * 更新时间
     */
    private Date updateTime;


}

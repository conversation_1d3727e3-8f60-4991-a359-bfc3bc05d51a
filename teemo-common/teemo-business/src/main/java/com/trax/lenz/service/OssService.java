package com.trax.lenz.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.trax.lenz.common.utils.DateUtil;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.config.prop.OssProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.UUID;


@Slf4j
@Service
public class OssService {

    private OSS ossClient;

    private OssProperties ossProperties;

    public OssService(OSS ossClient, OssProperties ossProperties) {
        this.ossClient = ossClient;
        this.ossProperties = ossProperties;
    }

    /**
     * 上传
     *
     * @param bytes
     * @param responseId
     */
    public String upload(String responseId, String bytes) {
        String ossPath = "rule1/teemo-service/temp-file/" + DateUtil.localDateTimeFormat(LocalDateTime.now(), DateUtil.DATE_FORMAT_10) + "/" + responseId + "/" + UUID.randomUUID().toString().replaceAll("-", "") + ".jpg";
        long startTime = System.currentTimeMillis();
        try {
            log.info("上传文件到阿里云OSS! ossPath={}", ossPath);
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossProperties.getBucketName(), ossPath, new ByteArrayInputStream(Base64.getMimeDecoder().decode(bytes)));
            ossClient.putObject(putObjectRequest);
            long times = System.currentTimeMillis() - startTime;
            log.info("上传文件耗时：times={}", times);
        } catch (Exception e) {
            log.error("uploadFileToAliyun exception", e);
        }
        return ossProperties.getDomain() + "/" + ossPath;
    }

    /**
     * 上传
     *
     * @param bytes
     */
    public String uploadV2(String ossPath, String bytes) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("上传文件到阿里云OSS! ossPath={}", ossPath);
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossProperties.getBucketName(), ossPath, new ByteArrayInputStream(bytes.getBytes()));
            ossClient.putObject(putObjectRequest);
            long times = System.currentTimeMillis() - startTime;
            log.info("上传文件耗时：times={}", times);
        } catch (Exception e) {
            log.error("uploadFileToAliyun exception", e);
        }
        return ossProperties.getDomain() + "/" + ossPath;
    }

    public String uploadV3(String ossPath, String base64EncodedBytes, String contentType) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("上传文件到阿里云OSS! ossPath={}", ossPath);
            byte[] decodedBytes = Base64.getMimeDecoder().decode(base64EncodedBytes);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(contentType);
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossProperties.getBucketName(), ossPath, new ByteArrayInputStream(decodedBytes), metadata);
            ossClient.putObject(putObjectRequest);
            long times = System.currentTimeMillis() - startTime;
            log.info("上传文件耗时：times={}，putObjectRequest={}", times, JsonUtil.toJsonString(putObjectRequest));
        } catch (Exception e) {
            log.error("uploadFileToAliyun exception", e);
        }
        log.info("ossProperties.getDomain() + ossPath={}", ossProperties.getDomain() + "/" + ossPath);
        return ossProperties.getDomain() + "/" + ossPath;
    }

    /**
     * 获取SDK上传图片路径
     *
     * @return
     */
    public String getSdkImagePath(String taskId, String responseId, String questionId, String imageName) {
        return ossProperties.getDomain() + "/" + LocalDateTime.now().getYear() + "/" + taskId + "/" + responseId + "/" + questionId + "/" + imageName;
    }
}

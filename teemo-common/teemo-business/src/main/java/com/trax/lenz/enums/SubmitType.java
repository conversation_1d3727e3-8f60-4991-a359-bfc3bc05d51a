package com.trax.lenz.enums;

import lombok.Getter;

/**
 * SubmitType
 *
 * @author: y<PERSON><PERSON>
 * @date: 2023-03-07
 */
@Getter
public enum SubmitType {

    AI_IDENTIFY(1, "AI识别"),

    MANUAL_IDENTIFY(2, "人工识别"),

    APPEAL(3, "申诉"),

    CHECK_REPEAT(4, "查重"),

    BATCH_CALC(5, "批量识别"),

    BATCH_APPEAL(6, "批量申诉");



    private final Integer code;

    private final String desc;

    SubmitType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}

package com.trax.lenz.service;

import com.google.common.collect.Maps;
import com.trax.lenz.entity.SubmitRequest;
import com.trax.lenz.paladin.domain.response.GetTaskConfigForTeemoResp;
import com.trax.lenz.service.impl.SubmitRequestServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.ssssssss.magicapi.core.service.MagicAPIService;

import java.util.Map;

/**
 * FunctionService
 *
 * @author: yuyang
 * @date: 2023-02-09
 */
@Slf4j
@Service
public class FunctionService {

    private MagicAPIService magicAPIService;

    private SubmitRequestServiceImpl submitRequestService;

    private PaladinService paladinService;

    public FunctionService(MagicAPIService magicAPIService, SubmitRequestServiceImpl submitRequestService, PaladinService paladinService) {
        this.magicAPIService = magicAPIService;
        this.submitRequestService = submitRequestService;
        this.paladinService = paladinService;
    }

    @Async(value = "businessExecutor")
    public void execSubmitFunction(String responseId) {
        log.info("执行提交函数！responseId={}", responseId);
        SubmitRequest submitRequest = submitRequestService.getOne(responseId);
        String taskId = submitRequest.getTaskId();
        GetTaskConfigForTeemoResp resp = paladinService.getTaskTeemoConfig(taskId);
        if (resp == null || StringUtils.isBlank(resp.getSubmitFunctionPath())) {
            return;
        }
        String funName = resp.getSubmitFunctionPath();
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("responseId", responseId);
        magicAPIService.invoke(funName, paramMap);
    }

    @Async(value = "businessExecutor")
    public void asyncExecMethod(String methodUrl, Map<String, Object> paramMap) {
        log.info("异步执行函数！methodUrl={}, paramMap={}", methodUrl, paramMap);
        if (StringUtils.isBlank(methodUrl)) {
            log.info("函数路径为空！停止执行！");
            return;
        }
        magicAPIService.invoke(methodUrl, paramMap);
    }

    public Object execMethod(String methodUrl, Map<String, Object> paramMap) {
        log.info("执行函数！methodUrl={}, paramMap={}", methodUrl, paramMap);
        if (StringUtils.isBlank(methodUrl)) {
            log.info("函数路径为空！停止执行！");
            return null;
        }
        return magicAPIService.invoke(methodUrl, paramMap);
    }
}

package com.trax.lenz.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trax.lenz.common.core.domain.R;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.dto.request.apiRepeat.ApiCustomerRepeatRequestReq;
import com.trax.lenz.dto.request.apiRepeat.ApiCustomerRepeatRequestReqV3;
import com.trax.lenz.dto.response.SubmitResp;
import com.trax.lenz.dto.response.apiRepeat.CallbackCustomerRepeatResultResp;
import com.trax.lenz.dto.response.apiRepeat.CallbackCustomerRepeatResultRespV3;
import com.trax.lenz.entity.CheckRepeatRequest;
import com.trax.lenz.entity.IdentifyRequestDetail;
import com.trax.lenz.entity.TaskConfig;
import com.trax.lenz.enums.SubmitType;
import com.trax.lenz.remote.KylinServiceFeignClient;
import com.trax.lenz.service.impl.CheckRepeatRequestServiceImpl;
import com.trax.lenz.service.impl.IdentifyRequestDetailServiceImpl;
import com.trax.lenz.service.impl.TaskConfigServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * CheckRepeatService查重服务处理类
 *
 * @author: yuyang
 * @date: 2024-01-24
 */
@Slf4j
@Service
public class CheckRepeatService {

    private final TaskConfigServiceImpl taskConfigService;

    private final FunctionService functionService;

    private final KylinServiceFeignClient kylinServiceFeignClient;

    private final CheckRepeatRequestServiceImpl checkRepeatRequestService;

    private final IdentifyRequestDetailServiceImpl identifyRequestDetailService;

    public CheckRepeatService(TaskConfigServiceImpl taskConfigService, FunctionService functionService, KylinServiceFeignClient kylinServiceFeignClient, CheckRepeatRequestServiceImpl checkRepeatRequestService, IdentifyRequestDetailServiceImpl identifyRequestDetailService) {
        this.taskConfigService = taskConfigService;
        this.functionService = functionService;
        this.kylinServiceFeignClient = kylinServiceFeignClient;
        this.checkRepeatRequestService = checkRepeatRequestService;
        this.identifyRequestDetailService = identifyRequestDetailService;
    }


    /**
     * 查重提交
     *
     * @param req 请求参数
     */
    public void submit(ApiCustomerRepeatRequestReq req) {
        R<SubmitResp> respR = kylinServiceFeignClient.checkRepeatV2(req);
        log.info("【查重提交】kylin返回值！result={}", JsonUtil.toJsonString(respR));
    }

    /**
     * 查重提交
     *
     * @param req 请求参数
     */
    public String submitV3(ApiCustomerRepeatRequestReqV3 req) {
        R<SubmitResp> respR = kylinServiceFeignClient.checkRepeatV3(req);
        log.info("【查重提交】kylin返回值！result={}", JsonUtil.toJsonString(respR));
        if (respR.isOk()) {
            return respR.getData().getRepeatId();
        }
        return null;
    }

    /**
     * 接口查重提交V4版本
     * 客户直接提供查重组
     *
     * @param req
     * @return
     */
    public String submitV4(ApiCustomerRepeatRequestReqV3 req) {
        R<SubmitResp> respR = kylinServiceFeignClient.checkRepeatV4(req);
        log.info("【查重提交】kylin返回值！result={}", JsonUtil.toJsonString(respR));
        if (respR.isOk()) {
            return respR.getData().getRepeatId();
        }
        return null;
    }


    /**
     * 查重回调
     *
     * @param req 回调参数
     */
    public void callbackV3(CallbackCustomerRepeatResultRespV3 req) {
        String customerRequestId = req.getCustomerRequestId();
        CheckRepeatRequest checkRepeatRequest = checkRepeatRequestService.getOneByCustomerRequestId(customerRequestId);
        if (checkRepeatRequest == null) {
            log.info("【查重回调】查不到customerRequestId={}，直接返回！", customerRequestId);
            return;
        }
        checkRepeatRequest.setKylinRespContent(JsonUtil.toJsonString(req));
        checkRepeatRequest.setStatus(3);
        checkRepeatRequest.setFinishTime(new Date());
        checkRepeatRequestService.update(checkRepeatRequest.getCheckRepeatId(), checkRepeatRequest);
        log.info("【查重回调】修改状态为处理完成！customerRequestId={}", customerRequestId);

        // 查询任务配置
        TaskConfig taskConfig = taskConfigService.getOne(checkRepeatRequest.getTaskId(), SubmitType.CHECK_REPEAT.getCode());

        // 执行前端查询方法
        Map<String, Object> params = new HashMap<>();
        params.put("checkRepeatId", checkRepeatRequest.getCheckRepeatId());
        functionService.execMethod(taskConfig.getCallbackMethod(), params);
    }

    /**
     * 查重回调
     *
     * @param req 回调参数
     */
    public void callback(CallbackCustomerRepeatResultResp req) {
        String checkRepeatId = req.getCustomerRequestId();
        CheckRepeatRequest checkRepeatRequest = checkRepeatRequestService.getOne(checkRepeatId);

        LambdaQueryWrapper<CheckRepeatRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CheckRepeatRequest::getCheckRepeatId, checkRepeatId);
        CheckRepeatRequest request = new CheckRepeatRequest();
        request.setStatus(3);
        request.setFinishTime(new Date());
        request.setKylinRespContent(JsonUtil.toJsonString(req));
        request.setUpdateTime(new Date());
        checkRepeatRequestService.update(request, wrapper);
        log.info("【查重回调】修改状态为处理完成！checkRepeatId={}", checkRepeatId);

        // 查询任务配置
        TaskConfig taskConfig = taskConfigService.getOne(checkRepeatRequest.getTaskId(), SubmitType.CHECK_REPEAT.getCode());

        // 执行前端查询方法
        Map<String, Object> params = new HashMap<>();
        params.put("checkRepeatId", checkRepeatId);
        functionService.execMethod(taskConfig.getCallbackMethod(), params);
    }

    public void callbackV4(CallbackCustomerRepeatResultRespV3 req) {
        String childId = req.getRepeatId();
        // 这里由于detail表的responseId分表策略, 查询必须要用responseId查询, 故查重提交时, 将responseId作为customerRequestId传输至查重平台
        String responseId = req.getCustomerRequestId();
        IdentifyRequestDetail oneByChildDetail = identifyRequestDetailService.getOneByResponseIdAndChildId(responseId, childId);
        if (oneByChildDetail == null) {
            log.info("【查重回调V4】查不到childId={}，直接返回！", childId);
            return;
        }
        oneByChildDetail.setChildRespContent(JsonUtil.toJsonString(req));
        oneByChildDetail.setStatus(3);
        oneByChildDetail.setFinishTime(new Date());
        identifyRequestDetailService.update(responseId, childId, oneByChildDetail);
        log.info("【查重回调V4】修改状态为处理完成！childId={}", childId);
        // 查询任务配置
        TaskConfig taskConfig = taskConfigService.getOne(req.getTaskId(), SubmitType.CHECK_REPEAT.getCode());
        // 执行前端查询方法
        Map<String, Object> params = new HashMap<>();
        params.put("childId", childId);
        params.put("responseId", responseId);
        functionService.execMethod(taskConfig.getCallbackMethod(), params);
    }
}

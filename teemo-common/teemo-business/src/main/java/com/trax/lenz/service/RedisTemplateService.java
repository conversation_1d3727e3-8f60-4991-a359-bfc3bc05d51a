package com.trax.lenz.service;

import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.config.prop.RedisProperties;
import com.trax.lenz.dto.redis.SendRedisMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * @ClassName RedisService
 * @Description:
 * <AUTHOR>
 * @Date 2023-08-17-18:56
 **/

@Slf4j
@Service
public class RedisTemplateService {

    private final RedisProperties redisProperties;

    private final RedisTemplate<String, Object> auditIngRedisTemplate;

    private final RedisTemplate<String, Object> auditDongRedisTemplate;

    private static final String TOKEN_KEY_PREFIX = "_mn_token_backlog";

    public RedisTemplateService(RedisProperties redisProperties, RedisTemplate<String, Object> auditIngRedisTemplate, RedisTemplate<String, Object> auditDongRedisTemplate) {
        this.redisProperties = redisProperties;
        this.auditIngRedisTemplate = auditIngRedisTemplate;
        this.auditDongRedisTemplate = auditDongRedisTemplate;
    }

    public String sendAuditIngRedisMessage(String message) {
        String key = redisProperties.getQueue().get(0).getQueue();
        Integer dataBase = redisProperties.getAuditIngDatabase();
        log.info("接收到发送redis消息请求，key={}, message={}, 指定库:{}", key, message, dataBase);
        SendRedisMessageDTO sendRedisMessageDTO = JsonUtil.jsonToPojo(message, SendRedisMessageDTO.class);
        // 发送字符串消息
        if (sendRedisMessageDTO != null) {
            ListOperations<String, Object> stringObjectListOperations = auditIngRedisTemplate.opsForList();
            Long aLong = stringObjectListOperations.leftPush(key, JsonUtil.toJsonString(sendRedisMessageDTO));
            log.info("发送redis消息完成! key={}, message={}, 指定库:{}, 返回结果:{}", key, message, dataBase, aLong);
        }
        return "success";
    }

    public String sendAuditDongRedisMessage(String message) {
        String key = redisProperties.getQueue().get(0).getQueue();
        Integer dataBase = redisProperties.getAuditDongDatabase();
        log.info("接收到发送redis消息请求，key={}, message={}, 指定库:{}", key, message, dataBase);
        SendRedisMessageDTO sendRedisMessageDTO = JsonUtil.jsonToPojo(message, SendRedisMessageDTO.class);
        // 发送字符串消息
        if (sendRedisMessageDTO != null) {
            ListOperations<String, Object> stringObjectListOperations1 = auditDongRedisTemplate.opsForList();
            Long aLong = stringObjectListOperations1.leftPush(key, JsonUtil.toJsonString(sendRedisMessageDTO));
            log.info("发送redis消息完成! key={}, message={}, 指定库:{}, 返回结果:{}", key, message, dataBase, aLong);
        }
        return "success";
    }

    /**
     * 设置值
     * @param key
     * @param initialValue 初始值
     * @param expireTime   过期时间（秒）
     * @return 是否设置成功
     */
    public boolean setRedisValue(String key, String initialValue, long expireTime) {
        try {
            String redisKey = TOKEN_KEY_PREFIX + key;
            // 检查key是否已存在
            if (Boolean.TRUE.equals(auditIngRedisTemplate.hasKey(redisKey))) {
                log.info("redis key已存在，不进行写入操作, key: {}", key);
                return false;
            }
            auditIngRedisTemplate.opsForValue().set(redisKey, initialValue, expireTime, TimeUnit.SECONDS);
            return true;
        } catch (Exception e) {
            log.error("设置redis值失败, key: {}, initialValue: {}, expireTime: {}", key, initialValue, expireTime, e);
            return false;
        }
    }

    /**
     * 获取当前值
     * @param key
     * @return 当前值
     */
    public String getRedisValue(String key) {
        try {
            String redisKey = TOKEN_KEY_PREFIX + key;
            Object value = auditIngRedisTemplate.opsForValue().get(redisKey);
            if (value != null) {
                if (value instanceof String) {
                    try {
                        return (String) value;
                    } catch (Exception e) {
                        throw new IllegalArgumentException("无法转换: " + value, e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取当前redis值失败, key: {}", key, e);
        }
        return "";
    }

    public void test() {
        String json = "{\"appealStatus\":2,\"sendRedisProductDTOList\":[{\"exist\":1,\"questionId\":1,\"productId\":28600}],\"responseId\":\"1762602042775371776\"}";
        sendAuditIngRedisMessage(json);
    }

}

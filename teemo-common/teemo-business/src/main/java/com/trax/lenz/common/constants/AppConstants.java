package com.trax.lenz.common.constants;

/**
 * 常量
 *
 * <AUTHOR>
 * @date 2022-02-22 17:02:02
 */
public class AppConstants {

    public static final String CALLBACK_POST_JSON = "post-json";
    public static final String CALLBACK_POST_FORM = "post-form";
    public static final String CALLBACK_GET = "get";

    public static final Integer POST_JSON = 1;
    public static final Integer GET_FORM = 2;
    public static final Integer POST_FORM = 3;

    public static final String HTTP_SUCCESS_CODE = "200";

    public static final Integer COMMON_SUCCESS_CODE = 200;

    public static final String GSK_SUCCESS_CODE = "0";

    public static final String STITCH = "stitch";

    public static final String BUSINESS_DATA_PARAM = "_lenz_";

    // 无用数据标识
    public static final String BACKUP = "_backup_";

    public static final String TRUE_STR = "true";

    public static final String CLOUD_PROVIDER_ALI = "aliyun";

    public static final String HEADER_TRACE_ID = "traceId";

    // 消息中间件类型
    public static final String KAFKA = "kafka";

}

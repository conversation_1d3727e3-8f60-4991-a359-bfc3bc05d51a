package com.trax.lenz.service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.trax.lenz.api.dto.enterprise.common.v2.CommonAiResultReq;
import com.trax.lenz.api.dto.enterprise.gsk.GskAiResultResp;
import com.trax.lenz.api.dto.response.GetAiResultLatestResp;
import com.trax.lenz.api.dto.response.GetIdentifyRequestResp;
import com.trax.lenz.api.dto.response.GetImageListResp;
import com.trax.lenz.api.dto.response.IdentifyFinishResp;
import com.trax.lenz.api.dto.response.RetCode;
import com.trax.lenz.api.dto.response.SubmitResp;
import com.trax.lenz.api.dto.submit.InnerSubmitReq;
import com.trax.lenz.api.remote.ApiServiceFeignClient;
import com.trax.lenz.azathoth.dto.common.base.product.RemoteProductDTO;
import com.trax.lenz.common.constants.AppConstants;
import com.trax.lenz.common.core.domain.R;
import com.trax.lenz.common.core.exception.BusinessException;
import com.trax.lenz.common.core.id.SnowFlakeFactory;
import com.trax.lenz.common.utils.CglibCopyBeanUtil;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.dto.StitchResult;
import com.trax.lenz.dto.request.IdentifyParameterReq;
import com.trax.lenz.dto.request.IdentifyReq;
import com.trax.lenz.dto.request.IdentifyReqCoordinate;
import com.trax.lenz.dto.request.IdentifyReqExtend;
import com.trax.lenz.dto.request.IdentifyReqGroup;
import com.trax.lenz.dto.request.IdentifyReqImage;
import com.trax.lenz.dto.response.CustomerImageRelationResp;
import com.trax.lenz.dto.response.GetImageSkuDetailResultResp;
import com.trax.lenz.dto.response.GetImageSkuResultResp;
import com.trax.lenz.dto.response.IdentifyParameterResp;
import com.trax.lenz.entity.SubmitRequest;
import com.trax.lenz.entity.SubmitRequestCallback;
import com.trax.lenz.message.protobuf.brainface.Aiobject;
import com.trax.lenz.message.protobuf.brainface.Interface;
import com.trax.lenz.message.protobuf.brainface.Util;
import com.trax.lenz.paladin.domain.old.product.RecognizeQuestionRowVO;
import com.trax.lenz.service.impl.SubmitRequestCallbackServiceImpl;
import com.trax.lenz.service.impl.SubmitRequestServiceImpl;
import com.trax.lenz.service.remote.ApiService;
import com.trax.lenz.service.remote.AzathothService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * 提交service
 *
 * @author: yuyang
 * @date: 2022-08-31 15:50
 */
@Slf4j
@Service
public class IdentifyService {

    @Value("${spring.profiles.active:''}")
    private String env;

    private SubmitRequestServiceImpl submitRequestService;

    private SubmitRequestCallbackServiceImpl submitRequestCallbackService;

    private SnowFlakeFactory snowFlakeFactory;

    private ApiServiceFeignClient apiServiceFeignClient;

    private PaladinService paladinService;

    private FunctionService functionService;

    private ProtoBufService protoBufService;

    private ApiService apiService;

    private AzathothService azathothService;

    public IdentifyService(SubmitRequestServiceImpl submitRequestService, SubmitRequestCallbackServiceImpl submitRequestCallbackService, SnowFlakeFactory snowFlakeFactory, ApiServiceFeignClient apiServiceFeignClient, PaladinService paladinService, FunctionService functionService, ProtoBufService protoBufService, ApiService apiService, AzathothService azathothService) {
        this.submitRequestService = submitRequestService;
        this.submitRequestCallbackService = submitRequestCallbackService;
        this.snowFlakeFactory = snowFlakeFactory;
        this.apiServiceFeignClient = apiServiceFeignClient;
        this.paladinService = paladinService;
        this.functionService = functionService;
        this.protoBufService = protoBufService;
        this.apiService = apiService;
        this.azathothService = azathothService;
    }

    /**
     * 获取环境（前端控制台使用）
     *
     * @return
     */
    public String getEnv() {
        return env;
    }

    /**
     * 保存提交流水（前端控制台使用）
     *
     * @param submitReqJson
     * @param identifyReqJson
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean save(String submitReqJson, String identifyReqJson) {
        SubmitRequest request = JsonUtil.jsonToPojo(submitReqJson, SubmitRequest.class);
        log.info("当前提交responseId:{}", request.getResponseId());
        try {
            submitRequestService.save(request);
        } catch (Exception e) {
            log.error("保存客户流水失败!{}, {}", e, e.getMessage());
            throw new BusinessException(RetCode.SYS_ERROR.getDesc());
        }
        // 提交识别请求到供应商
        return submit(identifyReqJson);
    }

    /**
     * 保存提交流水（前端控制台使用）
     *
     * @param submitReqJson
     * @return
     */
    public boolean saveIdentifyRequest(String submitReqJson) {
        SubmitRequest request = JsonUtil.jsonToPojo(submitReqJson, SubmitRequest.class);
        log.info("当前提交responseId:{}", request.getResponseId());
        submitRequestService.save(request);
        functionService.execSubmitFunction(request.getResponseId());
        return true;
    }

    /**
     * 提交校验拼接坐标数据是否合规
     *
     * @param groupList
     */
    private void checkCoordinate(List<IdentifyReqGroup> groupList) {
        List<IdentifyReqCoordinate> identifyReqCoordinateList = Lists.newArrayList();
        for (IdentifyReqGroup reqGroup : groupList) {
            if (Optional.ofNullable(reqGroup.getImageList()).isPresent() && paladinService.isStitchQuestion(reqGroup.getQuestionId())) {
                List<IdentifyReqImage> imageList = reqGroup.getImageList();
                IdentifyReqCoordinate reqCoordinate = new IdentifyReqCoordinate();
                reqCoordinate.setQuestionId(reqGroup.getQuestionId());
                List<IdentifyReqCoordinate.Coordinate> coordinateList = Lists.newArrayList();
                for (IdentifyReqImage image : imageList) {
                    IdentifyReqCoordinate.Coordinate coordinate = new IdentifyReqCoordinate.Coordinate();
                    coordinate.setRowNo(image.getRowNo());
                    coordinate.setColumnNo(image.getColumnNo());
                    coordinateList.add(coordinate);
                }
                reqCoordinate.setCoordinateList(coordinateList);
                identifyReqCoordinateList.add(reqCoordinate);
            }
        }

        for (IdentifyReqCoordinate reqCoordinate : identifyReqCoordinateList) {
            Integer oneSize = 0;
            List<IdentifyReqCoordinate.Coordinate> coordinateList = reqCoordinate.getCoordinateList();
            Map<Integer, List<IdentifyReqCoordinate.Coordinate>> rowNoMap = coordinateList.stream().collect(Collectors.groupingBy(IdentifyReqCoordinate.Coordinate::getRowNo));
            for (Integer rowNo : rowNoMap.keySet()) {
                List<IdentifyReqCoordinate.Coordinate> columnNoDescList = rowNoMap.get(rowNo).stream().sorted(Comparator.comparing(IdentifyReqCoordinate.Coordinate::getColumnNo).reversed()).collect(Collectors.toList());
                int columnSize = columnNoDescList.size();
                if (0 == oneSize) {
                    oneSize = columnSize;
                } else {
                    if (!oneSize.equals(columnSize)) {
                        throw new BusinessException("设置相邻组列数异常, 请核对后重新上传! 问题题目Id:" + reqCoordinate.getQuestionId(), 999);
                    }
                }
                for (IdentifyReqCoordinate.Coordinate coordinate : columnNoDescList) {
                    if (!coordinate.getColumnNo().equals(columnSize)) {
                        throw new BusinessException("设置拼接参数异常, 请核对后重新上传! 问题题目Id:" + reqCoordinate.getQuestionId(), 999);
                    }
                    columnSize--;
                }
            }
        }
    }

    /**
     * 查询提交流水（前端控制台使用）
     *
     * @param responseId
     * @return
     */
    public SubmitRequest getSubmitRequest(String responseId) {
        LambdaQueryWrapper<SubmitRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SubmitRequest::getResponseId, responseId);
        return submitRequestService.getOne(wrapper);
    }

    /**
     * 更新提交流水状态（前端控制台使用）
     *
     * @param responseId
     * @return
     */
    public void updateStatus(String responseId) {
        LambdaQueryWrapper<SubmitRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SubmitRequest::getResponseId, responseId);
        SubmitRequest request = new SubmitRequest();
        request.setStatus(2);
        request.setUpdateTime(new Date());
        submitRequestService.update(request, wrapper);
    }

    /**
     * 更新提交流水回调状态（前端控制台使用）
     *
     * @param responseId
     */
    public void updateCallbackStatusFinish(String responseId) {
        // 修改回调状态标识为已完成
        submitRequestService.updateCallbackStatusFinish(responseId);
    }

    /**
     * 提交识别请求到供应商/校验拼接参数是否合理
     *
     * @param identifyReqJson
     * @return
     */
    public boolean submit(String identifyReqJson) {
        IdentifyReq identifyReq = JsonUtil.jsonToPojo(identifyReqJson, IdentifyReq.class);
        String responseId = identifyReq.getResponseId();

        // 组装api请求对象
        InnerSubmitReq innerSubmitReq = new InnerSubmitReq();
        innerSubmitReq.setTaskId(identifyReq.getTaskId());
        innerSubmitReq.setResponseId(responseId);
        innerSubmitReq.setCustomerId(identifyReq.getCustomerId());
        innerSubmitReq.setRepeatSubmit(identifyReq.getRepeatSubmit());
        if (Optional.ofNullable(identifyReq.getCustomerRequestId()).isPresent()) {
            innerSubmitReq.setCustomerRequestId(identifyReq.getCustomerRequestId());
        }
        innerSubmitReq.setExtendInfoJson("");
        IdentifyReqExtend extendInfo = identifyReq.getExtendInfo();
        InnerSubmitReq.MetaData responseExtendInfo = new InnerSubmitReq.MetaData();
        CglibCopyBeanUtil.basicCopyBean(extendInfo, responseExtendInfo);
        innerSubmitReq.setMetaData(responseExtendInfo);
        innerSubmitReq.setSubmitMode(IdentifyReq.SUBMIT_MODE_INNER_TEEMO);

        List<InnerSubmitReq.Group> groupList = new ArrayList<>();

        // 校验拼接参数
        checkCoordinate(identifyReq.getGroupList());

        for (IdentifyReqGroup group : identifyReq.getGroupList()) {
            InnerSubmitReq.Group api_group = new InnerSubmitReq.Group();
            api_group.setGroupId(Integer.valueOf(group.getGroupId()));
            api_group.setQuestionId(group.getQuestionId());

            List<InnerSubmitReq.Group.Image> imageList = new ArrayList<>();
            for (IdentifyReqImage image : group.getImageList()) {
                // 组装api对象
                InnerSubmitReq.Group.Image api_image = new InnerSubmitReq.Group.Image();
                api_image.setImageId(image.getImageId());
                api_image.setImageName(image.getImageName());
                api_image.setImageUrl(image.getImageUrl());
                api_image.setRowNo(image.getRowNo());
                api_image.setColumnNo(image.getColumnNo());
                api_image.setImageType(image.getImageType());
                imageList.add(api_image);
            }
            api_group.setImageList(imageList);
            groupList.add(api_group);
        }
        innerSubmitReq.setGroupList(groupList);

        // 提交api进行识别
        log.info("提交报文：innerSubmitReq={}", JsonUtil.toJsonString(innerSubmitReq));
        R<SubmitResp> respR = apiServiceFeignClient.submit(innerSubmitReq);
        log.info("【teemo提交】识别厂商返回值：responseId={}, returnJson={}", responseId, JsonUtil.toJsonString(respR));
        if (respR.isOk() && respR.getData().isSuccess()) {
            return true;
        }
        if (999 == respR.getCode() && RetCode.BUSINESSDATA_REPEATED.getCode().equals(respR.getMsg())) {
            log.info("答卷提交API失败!, 错误码:{}", respR.getMsg());
            throw new BusinessException(RetCode.BUSINESSDATA_REPEATED.getDesc(), respR.getCode());
        }
        return false;
    }

    /**
     * 提交识别请求到供应商/校验拼接参数是否合理
     *
     * @param identifyReqJson
     * @return
     */
    public void submitV2(String responseId, String identifyReqJson) {
        IdentifyReq identifyReq = JsonUtil.jsonToPojo(identifyReqJson, IdentifyReq.class);

        // 组装api请求对象
        InnerSubmitReq innerSubmitReq = new InnerSubmitReq();
        innerSubmitReq.setTaskId(identifyReq.getTaskId());
        innerSubmitReq.setResponseId(responseId);
        innerSubmitReq.setCustomerId(identifyReq.getCustomerId());
        innerSubmitReq.setRepeatSubmit(identifyReq.getRepeatSubmit());
        if (Optional.ofNullable(identifyReq.getCustomerRequestId()).isPresent()) {
            innerSubmitReq.setCustomerRequestId(identifyReq.getCustomerRequestId());
        }
        innerSubmitReq.setExtendInfoJson("");
        IdentifyReqExtend extendInfo = identifyReq.getExtendInfo();
        InnerSubmitReq.MetaData responseExtendInfo = new InnerSubmitReq.MetaData();
        CglibCopyBeanUtil.basicCopyBean(extendInfo, responseExtendInfo);
        innerSubmitReq.setMetaData(responseExtendInfo);
        innerSubmitReq.setSubmitMode(IdentifyReq.SUBMIT_MODE_INNER_TEEMO);

        List<InnerSubmitReq.Group> groupList = new ArrayList<>();

        // 校验拼接参数
        checkCoordinate(identifyReq.getGroupList());

        for (IdentifyReqGroup group : identifyReq.getGroupList()) {
            InnerSubmitReq.Group api_group = new InnerSubmitReq.Group();
            api_group.setGroupId(Integer.valueOf(group.getGroupId()));
            api_group.setQuestionId(group.getQuestionId());

            List<InnerSubmitReq.Group.Image> imageList = new ArrayList<>();
            for (IdentifyReqImage image : group.getImageList()) {
                // 组装api对象
                InnerSubmitReq.Group.Image api_image = new InnerSubmitReq.Group.Image();
                api_image.setImageId(image.getImageId());
                api_image.setImageName(image.getImageName());
                api_image.setImageUrl(image.getImageUrl());
                api_image.setRowNo(image.getRowNo());
                api_image.setColumnNo(image.getColumnNo());
                api_image.setImageType(image.getImageType());
                imageList.add(api_image);
            }
            api_group.setImageList(imageList);
            groupList.add(api_group);
        }
        innerSubmitReq.setGroupList(groupList);

        // 提交api进行识别
        log.info("提交报文：innerSubmitReq={}", JsonUtil.toJsonString(innerSubmitReq));
        R<SubmitResp> respR = apiServiceFeignClient.submit(innerSubmitReq);
        log.info("【teemo提交】识别厂商返回值：responseId={}, returnJson={}", responseId, JsonUtil.toJsonString(respR));
        if (respR.isOk() && respR.getData().isSuccess()) {
            log.info("【teemo提交】提交成功！responseId:{}", responseId);
            LambdaQueryWrapper<SubmitRequest> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SubmitRequest::getResponseId, responseId);
            SubmitRequest request = new SubmitRequest();
            request.setStatus(2);
            request.setUpdateTime(new Date());
            submitRequestService.update(request, wrapper);
        } else {
            log.info("【teemo提交】提交失败！responseId:{}", responseId);
        }
        if (999 == respR.getCode() && RetCode.BUSINESSDATA_REPEATED.getCode().equals(respR.getMsg())) {
            log.info("答卷提交API失败!, 错误码:{}", respR.getMsg());
            throw new BusinessException(RetCode.BUSINESSDATA_REPEATED.getDesc(), respR.getCode());
        }
    }

    /**
     * 判断是否识别完成
     * <p>
     * teemo保存了答卷的状态，但存在2个问题，1、历史答卷teemo这边不存在 2、答卷重跑时，可能识别结果被清空了。综上直接去api查询实时状态
     *
     * @param responseId
     * @return
     */
    public boolean isFinish(String responseId) {
        try {
            R<IdentifyFinishResp> respR = apiServiceFeignClient.isFinish(responseId);
            if (respR.isOk() && respR.getData().isFinish()) {
                return true;
            }
        } catch (Exception e) {
            log.error("RPC查询答卷识别状态异常！responseId={}", responseId);
        }
        return false;
    }

    /**
     * 保存回调流水（前端控制台使用）
     *
     * @param json
     * @return
     */
    public void saveCallback(String json) {
        SubmitRequestCallback callback = JsonUtil.jsonToPojo(json, SubmitRequestCallback.class);
        SubmitRequest submitRequest = submitRequestService.getOne(callback.getResponseId());
        callback.setId(snowFlakeFactory.nextIdStr());
        callback.setProjectCode(submitRequest.getProjectCode());
        callback.setResponseId(callback.getResponseId());
        callback.setCreateTime(new Date());
        submitRequestCallbackService.save(callback);
    }

    /**
     * 查询图片之间的关系
     * 2023-01-03: 图片关系列表直接由api获取
     *
     * @param responseId
     * @return
     */
    public Map<String, CustomerImageRelationResp> getImageMap(String responseId) {
        List<CustomerImageRelationResp> imageList = Lists.newArrayList();
        try {
            log.info("RPC查询答卷图片列表请求参数responseId={}", responseId);
            R<GetImageListResp> respR = apiServiceFeignClient.getImageList(responseId);
            log.info("RPC查询答卷图片列表返回结果:{}", JsonUtil.toJsonString(respR));
            if (respR.isOk()) {
                List<GetImageListResp.Image> list = respR.getData().getList();
                for (GetImageListResp.Image image : list) {
                    CustomerImageRelationResp imageRelationResp = new CustomerImageRelationResp();
                    imageRelationResp.setImageId(image.getImageId());
                    imageRelationResp.setCustomerImageId(image.getCustomerImageId());
                    imageRelationResp.setCustomerImageName(image.getCustomerImageName());
                    imageRelationResp.setCustomerImageUrl(image.getCustomerImageUrl());
                    imageRelationResp.setImageServerPath(image.getImageServerPath());
                    imageRelationResp.setGroupNo(image.getGroupNo());
                    imageRelationResp.setCustomerImageGroupNo(image.getCustomerImageGroupNo());
                    imageList.add(imageRelationResp);
                }
            }
        } catch (Exception e) {
            log.error("RPC查询答卷图片列表异常！responseId={}", responseId);
        }
        if (CollectionUtils.isEmpty(imageList)) {
            return Maps.newHashMap();
        }
        return imageList.stream().collect(Collectors.toMap(CustomerImageRelationResp::getImageId, customerImageRelationResp -> customerImageRelationResp));
    }

    /**
     * 查询答卷提交流水信息（前端控制台使用）
     * 2022-12-22: teemo不再维护客户Id关系, 转去api查询
     *
     * @param req
     * @return
     */
    public IdentifyParameterResp getSubmitRequestByCustomerInfo(IdentifyParameterReq req) {
        IdentifyParameterResp resp = new IdentifyParameterResp();
        log.info("接收到RPC-API查询答卷流水信息!");
        try {
            GetIdentifyRequestResp requestInfo = getHistoryApiRequestInfo(req.getCompanyId(), req.getCustomerRequestIdList(), req.getResponseId());
            if (ObjectUtils.isNotEmpty(requestInfo)) {
                resp.setTaskId(requestInfo.getRequest().getTaskId());
                resp.setResponseId(requestInfo.getResponseId());
                resp.setCustomerRequestIdList(Arrays.asList(requestInfo.getRequest().getCustomerRequestId().split(AppConstants.BUSINESS_DATA_PARAM)));
            }
        } catch (Exception e) {
            log.info("RPC-API查询答卷流水信息异常! e:{},e:{}", e, e.getMessage());
        }
        return resp;
    }

    /**
     * 支持查询 api 历史数据
     *
     * @param customerId            客户ID,
     * @param customerRequestIdList
     * @param responseId
     * @return
     */
    public GetIdentifyRequestResp getHistoryApiRequestInfo(String customerId, List<String> customerRequestIdList, String responseId) {
        CommonAiResultReq req = new CommonAiResultReq();
        req.setResponseId(responseId);
        req.setCompanyId(customerId);
        req.setBusinessDataParamList(customerRequestIdList);
        try {
            R<GetIdentifyRequestResp> identifyRequest = apiServiceFeignClient.getIdentifyRequestByCustomerInformation(req);
            if (Optional.ofNullable(identifyRequest).isPresent() && AppConstants.COMMON_SUCCESS_CODE == identifyRequest.getCode()) {
                return identifyRequest.getData();
            }
        } catch (Exception e) {
            log.error("RPC-api获取答卷重复提交失败! e:{}, e:{}", e, e.getMessage());
        }
        return null;
    }

    /**
     * 客户Id组装
     *
     * @param strList
     * @return
     */
    public String splicingDataId(List<String> strList) {
        StringBuffer stringBuffer = new StringBuffer();
        for (String str : strList) {
            stringBuffer.append(str).append(AppConstants.BUSINESS_DATA_PARAM);
        }
        String ret = stringBuffer.toString();
        if (StringUtils.endsWith(ret, AppConstants.BUSINESS_DATA_PARAM)) {
            ret = StringUtils.substringBeforeLast(ret, AppConstants.BUSINESS_DATA_PARAM);
        }
        return ret;
    }

    /**
     * 项目字符串坐标转换成模型坐标
     *
     * @param coordinate 字符串坐标
     */
    public GskAiResultResp.AiResultDTO.Patches.Coordinate calCoordinate(String coordinate, String multiCoordinate) {
        GskAiResultResp.AiResultDTO.Patches.Coordinate coordinateObj = new GskAiResultResp.AiResultDTO.Patches.Coordinate();
        try {
            if (StringUtil.isBlank(coordinate)) {
                Set<Integer> setX = new TreeSet<>();
                Set<Integer> setY = new TreeSet<>();
                JSONArray jsonArray = JSONArray.parseArray(multiCoordinate);
                if (jsonArray.size() > 0) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONArray job = jsonArray.getJSONArray(i);
                        setX.add(job.getIntValue(0));
                        setY.add(job.getIntValue(1));
                    }
                }
                coordinateObj.setXmin(((TreeSet<Integer>) setX).first());
                coordinateObj.setXmax(((TreeSet<Integer>) setX).last());
                coordinateObj.setYmin(((TreeSet<Integer>) setY).first());
                coordinateObj.setYmax(((TreeSet<Integer>) setY).last());
            } else {
                String[] coordinateArray = coordinate.split(",");
                if (coordinateArray.length == 4) {
                    coordinateObj.setXmin(Integer.valueOf(coordinateArray[0]));
                    coordinateObj.setYmin(Integer.valueOf(coordinateArray[1]));
                    coordinateObj.setXmax(Integer.valueOf(coordinateArray[2]));
                    coordinateObj.setYmax(Integer.valueOf(coordinateArray[3]));
                }
            }
        } catch (Exception e) {
            coordinateObj.setXmin(0);
            coordinateObj.setYmin(0);
            coordinateObj.setXmax(0);
            coordinateObj.setYmax(0);
        }
        return coordinateObj;
    }

    /**
     * 获取拼接结果集
     *
     * @param responseId
     * @return
     */
    public List<StitchResult> getStitchResult(String responseId) {
        log.info("获取拼接结果！responseId={}", responseId);
        List<StitchResult> aiStitchResult = new ArrayList<>();
        GetAiResultLatestResp aiResultLatest = apiService.getAiResultLatestV2(responseId);
        if (Objects.isNull(aiResultLatest)) {
            return aiStitchResult;
        }
        try {
            // 获取客户图片对应关系
            Map<String, CustomerImageRelationResp> imageMap = apiService.getImageMap(responseId);
            List<GetAiResultLatestResp.Question> questionList = aiResultLatest.getQuestionList();
            for (GetAiResultLatestResp.Question question : questionList) {
                Interface.FullAIResponse fullAiResponse = protoBufService.getFullAiResponse(question.getResultJsonPath());
                for (Interface.ResponseGroup group : fullAiResponse.getResponseGroupsList()) {
                    String stitchMarkImageUrl = group.getStitchResult().getStitchMarkImageUrl();
                    String stitchImageUrl = group.getStitchResult().getStitchImageUrl();
                    if (StringUtils.isBlank(stitchImageUrl)) {
                        continue;
                    }
                    List<String> imageIdList = Lists.newArrayList();
                    String customerGroupId = "";
                    for (Interface.SingleSourceResult singleSourceResult : group.getSingleSourceResultsList()) {
                        if (imageMap.containsKey(singleSourceResult.getSource().getId())) {
                            imageIdList.add(imageMap.get(singleSourceResult.getSource().getId()).getCustomerImageId());
                            customerGroupId = imageMap.get(singleSourceResult.getSource().getId()).getCustomerImageGroupNo();
                        }
                    }
                    StitchResult stitchResult = new StitchResult();
                    stitchResult.setStitchImageUrl(stitchImageUrl);
                    stitchResult.setStitchMarkImageUrl(stitchMarkImageUrl);
                    stitchResult.setSourceImageIdList(imageIdList);
                    stitchResult.setGroupId(String.valueOf(group.getGroupNo()));
                    stitchResult.setCustomerGroupId(customerGroupId);
                    aiStitchResult.add(stitchResult);
                }
            }
        } catch (Exception e) {
            log.error("组装拼接结果异常！responseId={}", responseId, e);
        }
        return aiStitchResult;
    }

    /**
     * 获取单图结果
     *
     * @param responseId
     * @return
     */
    public GetImageSkuResultResp getImageSkuResult(String responseId) {
        GetAiResultLatestResp aiResultLatest = apiService.getAiResultLatestV2(responseId);
        if (Objects.isNull(aiResultLatest)) {
            return null;
        }
        List<String> targetProductIdList = null;
        List<GetImageSkuResultResp.Image> imageList = new ArrayList<>();
        for (GetAiResultLatestResp.Question question : aiResultLatest.getQuestionList()) {
            Interface.FullAIResponse fullAiResponse = protoBufService.getFullAiResponse(question.getResultJsonPath());
            // 获取识别模型类型
            Long responseType = fullAiResponse.getResponseType();
            // 获取客户图片对应关系
            Map<String, CustomerImageRelationResp> imageMap = apiService.getImageMap(responseId);
            List<Interface.ResponseGroup> responseGroupsList = fullAiResponse.getResponseGroupsList();
            // 识别目标集合
            List<Long> productIdList = Lists.newArrayList();
            for (Interface.ResponseGroup responseGroup : responseGroupsList) {
                for (Interface.SingleSourceResult singleSourceResult : responseGroup.getSingleSourceResultsList()) {
                    GetImageSkuResultResp.Image image = new GetImageSkuResultResp.Image();
                    String imageId = singleSourceResult.getSource().getId();
                    if (!imageMap.containsKey(imageId)) {
                        log.info("根据AI识别结果图片id未匹配到图片! responseId={}, imageId={}", responseId, imageId);
                        continue;
                    }
                    CustomerImageRelationResp identifyRequestImage = imageMap.get(imageId);
                    image.setImageId(identifyRequestImage.getImageId());
                    image.setCustomerImageId(identifyRequestImage.getCustomerImageId());
                    if (StringUtils.isNotBlank(singleSourceResult.getRectificationImageUrl())) {
                        image.setImageUrl(singleSourceResult.getRectificationImageUrl());
                    } else {
                        image.setImageUrl(singleSourceResult.getImageUrl());
                    }
                    image.setIsRemake(singleSourceResult.getIsRemake() ? 1 : 0);
                    image.setHeight(singleSourceResult.getHeight());
                    image.setWidth(singleSourceResult.getWidth());

                    // 解析场景
                    List<GetImageSkuResultResp.Image.Scene> sceneList = Lists.newArrayList();
                    List<Aiobject.Scene> aiSceneList = singleSourceResult.getScenesList();
                    for (Aiobject.Scene aiScene : aiSceneList) {
                        GetImageSkuResultResp.Image.Scene scene = new GetImageSkuResultResp.Image.Scene();
                        scene.setSceneId(aiScene.getId());
                        scene.setSceneName(aiScene.getName());
                        List<GetImageSkuResultResp.CoordinateDTO> pointList = getPointList(aiScene.getCoordinateList());
                        scene.setCoordinateDTOList(pointList);
                        sceneList.add(scene);
                    }
                    image.setSceneList(sceneList);

                    // 解析SKU
                    Integer maxSection = 0;
                    Integer maxLayer = 0;
                    List<GetImageSkuResultResp.Image.Product.Patch> patchList = Lists.newArrayList();
                    for (Aiobject.Patch aiPatch : singleSourceResult.getPatchesList()) {
                        long productId = responseType != 0 ? aiPatch.getEntityId() : aiPatch.getProductId();
                        if (productId < 0) {
                            continue;
                        }
                        if (targetProductIdList != null && !targetProductIdList.contains(String.valueOf(aiPatch.getProductId()))) {
                            continue;
                        }
                        GetImageSkuResultResp.Image.Product.Patch patch = new GetImageSkuResultResp.Image.Product.Patch();
                        patch.setProductId(productId);
                        patch.setLabel(aiPatch.getLabel());
                        patch.setPatchType((int) aiPatch.getPatchType());
                        patch.setSceneCode(aiPatch.getScene());
                        List<GetImageSkuResultResp.CoordinateDTO> pointList = getPointList(aiPatch.getCoordinateList());
                        patch.setScore(aiPatch.getScore());
                        patch.setCoordinateDTOList(pointList);
                        // 节
                        int section = (int) aiPatch.getPositionNo();
                        if (section > maxSection) {
                            maxSection = section;
                        }
                        patch.setSection(section);
                        // 层
                        int layer = (int) aiPatch.getLayerNo();
                        if (layer > maxLayer) {
                            maxLayer = layer;
                        }
                        patch.setLayer(layer);
                        patch.setIsFacing(aiPatch.getPositionNo() == 0 ? 1 : 0);
                        patchList.add(patch);
                        productIdList.add(productId);
                    }

                    // 分组计算总数
                    List<GetImageSkuResultResp.Image.Product> productList = Lists.newArrayList();
                    Map<Long, List<GetImageSkuResultResp.Image.Product.Patch>> map = patchList.stream().collect(Collectors.groupingBy(GetImageSkuResultResp.Image.Product.Patch::getProductId));
                    log.info("responseId:{}, 单图productIdList:{}", responseId, productIdList);
                    for (Long k : map.keySet()) {
                        GetImageSkuResultResp.Image.Product product = new GetImageSkuResultResp.Image.Product();
                        product.setProductId(k);
                        product.setTotal(map.get(k).size());
                        product.setPatchList(map.get(k));
                        productList.add(product);
                    }
                    image.setProductList(productList);
                    imageList.add(image);
                }
            }
            productIdList = productIdList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            log.info("responseId:{}, 最终productIdList:{}", responseId, productIdList);
            if (CollectionUtils.isNotEmpty(productIdList)) {
                Map<Long, RemoteProductDTO> productMap = azathothService.getProductMap(productIdList, responseType);
                if (Objects.nonNull(productMap)) {
                    for (GetImageSkuResultResp.Image image : imageList) {
                        for (GetImageSkuResultResp.Image.Product product : image.getProductList()) {
                            try {
                                product.setProductName("");
                                if (productMap.containsKey(product.getProductId())) {
                                    product.setProductName(productMap.get(product.getProductId()).getCnName());
                                    product.setCustomProductName(productMap.get(product.getProductId()).getCustomerName());
                                    if (Optional.ofNullable(productMap.get(product.getProductId()).getBarcode()).isPresent()) {
                                        product.setBarcode(productMap.get(product.getProductId()).getBarcode());
                                    } else {
                                        product.setBarcode("");
                                    }
                                }
                            } catch (Exception e) {
                                log.info("根据productId获取商品名称异常, responseId:{}", responseId, e);
                            }
                        }
                    }
                }
            }
        }
        return GetImageSkuResultResp.builder().imageList(imageList).build();
    }

    public List<GetImageSkuResultResp.CoordinateDTO> getPointList(List<Aiobject.Point> aiPointList) {
        List<GetImageSkuResultResp.CoordinateDTO> pointList = Lists.newArrayList();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(aiPointList)) {
            for (Aiobject.Point point : aiPointList) {
                GetImageSkuResultResp.CoordinateDTO coordinateDTO = new GetImageSkuResultResp.CoordinateDTO();
                coordinateDTO.setX(point.getX());
                coordinateDTO.setY(point.getY());
                pointList.add(coordinateDTO);
            }
        }
        return pointList;
    }

    /**
     * 获取单图结果
     *
     * @param responseId
     * @return
     */
    public GetImageSkuDetailResultResp getImageSkuDetailResult(String responseId) {
        GetAiResultLatestResp aiResultLatest = apiService.getAiResultLatestV2(responseId);
        if (Objects.isNull(aiResultLatest)) {
            return null;
        }
        List<GetImageSkuDetailResultResp.Image> imageList = new ArrayList<>();
        for (GetAiResultLatestResp.Question question : aiResultLatest.getQuestionList()) {
            Interface.FullAIResponse fullAiResponse = protoBufService.getFullAiResponse(question.getResultJsonPath());
            long responseType = fullAiResponse.getResponseType();
            // 获取任务题目对应步骤信息
            List<RecognizeQuestionRowVO> recognizeQuestionRowVOList = paladinService.getRecognizeQuestionRow(fullAiResponse.getBusinessFields().getTaskId());
            // 从集合中过滤当前题目对应步骤关系
            Map<String, List<String>> recognizeStepMap = recognizeQuestionRowVOList.stream().collect(Collectors.toMap(RecognizeQuestionRowVO::getQuestionId, RecognizeQuestionRowVO::getRecognizeStepList));
            List<String> recognizeList = recognizeStepMap.get(question.getQuestionId());

            // 获取客户图片对应关系
            Map<String, CustomerImageRelationResp> imageMap = apiService.getImageMap(responseId);
            List<Interface.ResponseGroup> responseGroupsList = fullAiResponse.getResponseGroupsList();
            // 识别目标集合
            List<Long> productIdList = Lists.newArrayList();
            for (Interface.ResponseGroup responseGroup : responseGroupsList) {
                for (Interface.SingleSourceResult singleSourceResult : responseGroup.getSingleSourceResultsList()) {
                    GetImageSkuDetailResultResp.Image image = new GetImageSkuDetailResultResp.Image();
                    String imageId = singleSourceResult.getSource().getId();
                    if (!imageMap.containsKey(imageId)) {
                        log.info("根据AI识别结果图片id未匹配到图片! responseId={}, imageId={}", responseId, imageId);
                        continue;
                    }
                    CustomerImageRelationResp identifyRequestImage = imageMap.get(imageId);
                    image.setQuestionId(question.getQuestionId());
                    image.setImageId(identifyRequestImage.getImageId());
                    image.setCustomerImageId(identifyRequestImage.getCustomerImageId());
                    image.setCustomerImageName(identifyRequestImage.getCustomerImageName());
                    image.setGroupNo(identifyRequestImage.getGroupNo());
                    image.setCustomerGroupNo(identifyRequestImage.getCustomerImageGroupNo());
                    image.setIsRemake("-1");
                    image.setRemakeScore("0");
                    image.setRepeat("-1");
                    image.setRepeatGroup("-1");
                    if (recognizeList.contains("质量")) {
                        image.setIsRemake(String.valueOf(singleSourceResult.getIsRemake() ? 1 : 0));
                        image.setRemakeScore(String.valueOf(singleSourceResult.getRemakeScore()));
                    }
                    if (StringUtils.isNotBlank(singleSourceResult.getRectificationImageUrl())) {
                        image.setImageUrl(singleSourceResult.getRectificationImageUrl());
                    } else {
                        image.setImageUrl(singleSourceResult.getImageUrl());
                    }
                    image.setHeight(singleSourceResult.getHeight());
                    image.setWidth(singleSourceResult.getWidth());
                    // 解析场景
                    List<Aiobject.Patch> patchesList = singleSourceResult.getPatchesList();
                    image.setSceneList(Lists.newArrayList());
                    if (CollectionUtils.isNotEmpty(patchesList)) {
                        // 单图结果按照场景分组
                        Map<String, List<Aiobject.Patch>> sceneMap = patchesList.stream().filter(aiPatch -> aiPatch.getProductId() > 0).collect(Collectors.groupingBy(Aiobject.Patch::getScene));

                        List<GetImageSkuDetailResultResp.Image.Scene> sceneList = Lists.newArrayList();
                        for (String sceneId : sceneMap.keySet()) {
                            GetImageSkuDetailResultResp.Image.Scene scene = new GetImageSkuDetailResultResp.Image.Scene();
                            scene.setSceneId(sceneId);
                            scene.setSceneName(sceneId);
                            List<Aiobject.Patch> patchList = sceneMap.get(sceneId);
                            // 处理场景内商品
                            List<GetImageSkuDetailResultResp.Image.Product> productList = Lists.newArrayList();
                            for (Aiobject.Patch patch : patchList) {
                                long productId = responseType != 0 ? patch.getEntityId() : patch.getProductId();
                                if (productId < 0) {
                                    continue;
                                }
                                GetImageSkuDetailResultResp.Image.Product product = new GetImageSkuDetailResultResp.Image.Product();
                                product.setProductId(productId);
                                product.setScore(String.valueOf(patch.getScore()));
                                product.setIsMasked(patch.getIsMasked());
                                product.setIsChecked(patch.getIsChecked());
                                // 识别商品集合
                                productIdList.add(productId);
                                product.setLayer((int) patch.getLayerNo());
                                product.setIsFacing(patch.getPositionNo() == 0 ? 1 : 0);
                                List<GetImageSkuResultResp.CoordinateDTO> pointList = getPointList(patch.getCoordinateList());
                                product.setCoordinateDTOList(pointList);
                                // TODO 价格目前暂不计算
                                productList.add(product);
                            }
                            scene.setProductList(productList);
                            sceneList.add(scene);
                        }
                        image.setSceneList(sceneList);
                    }
                    imageList.add(image);
                }
                // 组装查重结果
                if (recognizeList.contains("相似排重") || recognizeList.contains("门头排重") || recognizeList.contains("完全排重")) {
                    Interface.ImageDuplicationResult imageDuplicationResult = responseGroup.getImageDuplicationResult();
                    List<Interface.ImageDuplicationGroup> imageDuplicationGroupsList = imageDuplicationResult.getImageDuplicationGroupsList();
                    Map<String, GetImageSkuDetailResultResp.Image> imageResultByImageIdMap = imageList.stream().collect(Collectors.toMap(GetImageSkuDetailResultResp.Image::getImageId, image -> image));
                    // imageList中repeat字段附默认值0, repeatGroup字段附默认值""
                    imageList.forEach(image -> {
                        image.setRepeat("0");
                        image.setRepeatGroup("");
                    });
                    if (CollectionUtils.isEmpty(imageDuplicationGroupsList)) {
                        continue;
                    }
                    int i = 1;
                    for (Interface.ImageDuplicationGroup imageDuplicationGroup : imageDuplicationGroupsList) {
                        List<Util.SourceFlow> imagesList = imageDuplicationGroup.getImagesList();
                        if (CollectionUtils.isEmpty(imagesList)) {
                            continue;
                        }
                        for (Util.SourceFlow sourceFlow : imagesList) {
                            if (imageResultByImageIdMap.containsKey(sourceFlow.getId())) {
                                GetImageSkuDetailResultResp.Image image = imageResultByImageIdMap.get(sourceFlow.getId());
                                image.setRepeat("1");
                                image.setRepeatGroup(String.valueOf(i));
                            }
                        }
                        i++;
                    }
                }
            }
            productIdList = productIdList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            log.info("responseId:{}, 最终productIdList:{}", responseId, productIdList);
            if (CollectionUtils.isNotEmpty(productIdList)) {
                Map<Long, RemoteProductDTO> productMap = azathothService.getProductMap(productIdList, responseType);
                if (Objects.nonNull(productMap)) {
                    for (GetImageSkuDetailResultResp.Image image : imageList) {
                        for (GetImageSkuDetailResultResp.Image.Scene scene : image.getSceneList()) {
                            for (GetImageSkuDetailResultResp.Image.Product product : scene.getProductList()) {
                                try {
                                    product.setProductName("");
                                    if (productMap.containsKey(product.getProductId())) {
                                        product.setCustomerCode(productMap.get(product.getProductId()).getCustomerCode());
                                        product.setProductName(productMap.get(product.getProductId()).getCnName());
                                        product.setCustomProductName(productMap.get(product.getProductId()).getCustomerName());
                                    }
                                } catch (Exception e) {
                                    log.info("根据productId获取商品名称异常, responseId:{}", responseId, e);
                                }
                            }
                        }
                    }
                }
            }
        }
        return GetImageSkuDetailResultResp.builder().imageList(imageList).build();
    }
}

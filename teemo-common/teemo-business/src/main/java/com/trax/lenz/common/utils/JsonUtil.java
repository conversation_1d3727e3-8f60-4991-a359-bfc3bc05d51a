package com.trax.lenz.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.google.common.base.Strings;
import com.trax.lenz.common.core.json.CustomBeanSerializerModifier;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;

/**
 * Json转换工具类
 *
 * <AUTHOR>
 * @date 2022-02-09 11:12:44
 */
@Slf4j
public class JsonUtil {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    static {
        // 未找到字段不报错
        MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        // 空属性对象
        MAPPER.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
    
        MAPPER.getSerializerFactory().withSerializerModifier(new CustomBeanSerializerModifier());
        // null不进行序列化
        MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        //Jackson将对象转换为json字符串时，设置默认的时间格式
        DateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        MAPPER.setDateFormat(dateformat);

        // 防止Double类型序列号后显示科学记数法，保留4位小数
        SimpleModule module = new SimpleModule();
        module.addSerializer(Double.class, new StdSerializer(BigDecimal.class) {
            @Override
            public void serialize(Object o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
                BigDecimal bigDecimal = new BigDecimal(o.toString());
                bigDecimal = bigDecimal.setScale(4, BigDecimal.ROUND_UP);
                jsonGenerator.writeNumber(bigDecimal);
            }
        });
        MAPPER.registerModule(module);

        // 将null的属性转换为空串
//        MAPPER.getSerializerProvider().setNullValueSerializer(new JsonSerializer<Object>() {
//            @Override
//            public void serialize(Object o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException, JsonProcessingException {
//                jsonGenerator.writeString("");
//            }
//        });

    }

    /**
     * 对象转json
     *
     * @param obj 要转换的对象
     * @return String
     * @date 11/9/18 AM11:57
     */
    public static String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            log.error("JSON 序列化错误", e);
        }
        return null;
    }

    /**
     * 对象转json
     *
     * @param jsonData json数据
     * @param beanType 转换对象类型
     * @return T
     * @date 11/9/18 AM11:57
     */
    public static <T> T jsonToPojo(String jsonData, Class<T> beanType) {
        if (Strings.isNullOrEmpty(jsonData)) {
            return null;
        }
        try {
            return MAPPER.readValue(jsonData, beanType);
        } catch (Exception e) {
            log.error("JSON 反序列化错误", e);
        }

        return null;
    }

    /**
     * 对象转json
     *
     * @param jsonData      json数据
     * @param typeReference 转换对象类型
     * @return T
     * @date 11/9/18 AM11:57
     */
    public static <T> T parseObject(String jsonData, TypeReference<T> typeReference) {
        if (Strings.isNullOrEmpty(jsonData)) {
            return null;
        }
        try {
            return MAPPER.readValue(jsonData, typeReference);
        } catch (Exception e) {
            log.error("JSON 反序列化错误", e);
        }
        return null;
    }

    /**
     * 对象转json保存null字段,禁止引用同名字段
     *
     * @param object
     * @return
     */
    public static String obj2json(Object object) {
        return JSON.toJSONStringWithDateFormat(object, JSON.DEFFAULT_DATE_FORMAT,
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.DisableCircularReferenceDetect,
                SerializerFeature.WriteBigDecimalAsPlain);
    }


    public static ObjectMapper getObjectMapper(){
        return MAPPER;
    }
}

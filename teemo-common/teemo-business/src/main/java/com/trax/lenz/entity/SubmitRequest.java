package com.trax.lenz.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 客户识别请求记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SubmitRequest extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 答卷id
     */
    private String responseId;

    /**
     * 项目code（参见paladin）
     */
    private String projectCode;

//    /**
//     * 唯一请求id
//     */
//    private String customerRequestId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 答卷状态 1xxx.待处理 2xxx.识别中 3xxx.成功 4xxx.失败
     */
    private Integer status;

    /**
     * 回调标识 0.未完成 1.已完成
     */
    private Integer callbackStatus;

    /**
     * 请求内容（存储请求原文）
     */
    private String reqContent;

    /**
     * 测试（0.否 1.是）
     */
    private Integer test;

    /**
     * 提交url
     */
    private String submitUrl;

    /**
     * 回调url
     */
    private String callbackUrl;

    /**
     * 更新时间
     */
    private Date updateTime;

}

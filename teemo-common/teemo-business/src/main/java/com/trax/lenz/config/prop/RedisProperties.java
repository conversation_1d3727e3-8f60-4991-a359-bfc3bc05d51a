package com.trax.lenz.config.prop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * redis配置文件
 *
 * <AUTHOR>
 * @date 2021-04-29 13:59:13
 */
@Data
@ConfigurationProperties (prefix = "spring.redis")
@Component
public class RedisProperties {

  private String host;

  private Integer port;

  private String username;

  private String password;

  private Integer auditIngDatabase;

  private Integer auditDongDatabase;

  private List<RedisQueue> queue;

  @Data
  public static class RedisQueue {

    private String key;

    private String queue;

  }
}

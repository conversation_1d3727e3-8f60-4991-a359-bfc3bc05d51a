package com.trax.lenz.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * html处理相关工具类,  如有需要, 可基于Hutool做相应扩展
 *
 * <AUTHOR> Sun
 * @version 1.0
 * @date 2023-01-30 14:29
 */
public class HtmlUtil {

    /**
     * 去除富文本标签 Hutool 此处有bug
     *
     * @param htmlStr 原始文本
     * @return
     */
    public static String delHTMLTag(String htmlStr) {

        //定义script的正则表达式
        String regExScript = "<script[^>]*?>[\\s\\S]*?<\\/script>";
        //定义style的正则表达式
        String regExStyle = "<style[^>]*?>[\\s\\S]*?<\\/style>";
        //定义HTML标签的正则表达式
        String regExHtml = "<[^>]+>";

        Pattern pScript = Pattern.compile(regExScript, Pattern.CASE_INSENSITIVE);
        Matcher mScript = pScript.matcher(htmlStr);
        //过滤script标签
        htmlStr = mScript.replaceAll("");

        Pattern pStyle = Pattern.compile(regExStyle, Pattern.CASE_INSENSITIVE);
        Matcher mStyle = pStyle.matcher(htmlStr);
        //过滤style标签
        htmlStr = mStyle.replaceAll("");

        Pattern pHtml = Pattern.compile(regExHtml, Pattern.CASE_INSENSITIVE);
        Matcher mHtml = pHtml.matcher(htmlStr);
        //过滤html标签
        htmlStr = mHtml.replaceAll("");

        //返回文本字符串
        return htmlStr.trim();
    }

}

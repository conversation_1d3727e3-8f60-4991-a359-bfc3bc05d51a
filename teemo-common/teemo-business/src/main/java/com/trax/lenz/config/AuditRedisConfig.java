package com.trax.lenz.config;

import com.trax.lenz.config.prop.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 *  * @ClassName RedisService
 *  * @Description: redis配置
 *  * <AUTHOR>
 *  * @Date 2023-08-18-18:56
 */
@Configuration
public class AuditRedisConfig {

	private final RedisProperties redisProperties;

	public AuditRedisConfig(RedisProperties redisProperties) {
		this.redisProperties = redisProperties;
	}

	@Bean(name = "auditIngRedisTemplate")
	public RedisTemplate<String, Object> auditIngRedisTemplate() {
		RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
		config.setHostName(redisProperties.getHost());
		config.setPort(redisProperties.getPort());
		config.setDatabase(redisProperties.getAuditIngDatabase());
		config.setPassword(redisProperties.getPassword());
		LettuceConnectionFactory lettuceConnectionFactory = new LettuceConnectionFactory(config);
		lettuceConnectionFactory.afterPropertiesSet();
		RedisTemplate<String, Object> template = new RedisTemplate<>();
		template.setConnectionFactory(lettuceConnectionFactory);
		// 设置序列化器等其他配置
		template.setKeySerializer(keySerializer());
		template.setHashKeySerializer(keySerializer());
		template.setValueSerializer(valueSerializer());
		template.setHashValueSerializer(valueSerializer());
		return template;
	}

	@Bean(name = "auditDongRedisTemplate")
	public RedisTemplate<String, Object> auditDongRedisTemplate() {
		RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
		config.setHostName(redisProperties.getHost());
		config.setPort(redisProperties.getPort());
		config.setDatabase(redisProperties.getAuditDongDatabase());
		config.setPassword(redisProperties.getPassword());
		LettuceConnectionFactory lettuceConnectionFactory = new LettuceConnectionFactory(config);
		lettuceConnectionFactory.afterPropertiesSet();
		RedisTemplate<String, Object> template = new RedisTemplate<>();
		template.setConnectionFactory(lettuceConnectionFactory);
		// 设置序列化器等其他配置
		template.setKeySerializer(keySerializer());
		template.setHashKeySerializer(keySerializer());
		template.setValueSerializer(valueSerializer());
		template.setHashValueSerializer(valueSerializer());
		return template;
	}

	private RedisSerializer<String> keySerializer(){
		return new StringRedisSerializer();
	}

	/**
	 * 使用Jackson序列化器
	 * @return
	 */
	private RedisSerializer<Object> valueSerializer(){
		return new GenericJackson2JsonRedisSerializer();
	}

}
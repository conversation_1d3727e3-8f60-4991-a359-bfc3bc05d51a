package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trax.lenz.common.utils.CompressionUtil;
import com.trax.lenz.entity.PecRepeatRequestDetail;
import com.trax.lenz.mapper.PecRepeatRequestDetailMapper;
import com.trax.lenz.service.IPecRepeatRequestDetailService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Base64;
import java.util.List;

/**
 * <p>
 * 统一查重请求记录明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
public class PecRepeatRequestDetailServiceImpl extends ServiceImpl<PecRepeatRequestDetailMapper, PecRepeatRequestDetail> implements IPecRepeatRequestDetailService {

    public PecRepeatRequestDetail getOneDetail(String detailId) {
        LambdaQueryWrapper<PecRepeatRequestDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PecRepeatRequestDetail::getDetailId, detailId);
        PecRepeatRequestDetail detail = getOne(wrapper);
        try {
            if (StringUtils.isNotBlank(detail.getImageCompareJson())) {
                byte[] compressedData = Base64.getDecoder().decode(detail.getImageCompareJson());
                detail.setImageCompareJson(CompressionUtil.decompress(compressedData));
            }
            if (StringUtils.isNotBlank(detail.getImageOriginJson())) {
                byte[] compressedData = Base64.getDecoder().decode(detail.getImageOriginJson());
                detail.setImageOriginJson(CompressionUtil.decompress(compressedData));
            }
        } catch (IOException e) {
            log.error("压缩字段失败", e);
        }
        return detail;
    }

    public boolean saveEntity(PecRepeatRequestDetail detail) {
        try {
            if (StringUtils.isNotBlank(detail.getImageCompareJson())) {
                // 压缩 ImageCompareJson 字段
                byte[] compressedData = CompressionUtil.compress(detail.getImageCompareJson());
                detail.setImageCompareJson(Base64.getEncoder().encodeToString(compressedData));
            }
            if (StringUtils.isNotBlank(detail.getImageOriginJson())) {
                // 压缩 ImageOriginJson 字段
                byte[] compressedData = CompressionUtil.compress(detail.getImageOriginJson());
                detail.setImageOriginJson(Base64.getEncoder().encodeToString(compressedData));
            }
            // 使用 MyBatis-Plus 的 save 方法保存数据
            return save(detail);
        } catch (IOException e) {
            log.error("压缩字段失败", e);
            return false;
        }
    }

    public List<PecRepeatRequestDetail> getList(String checkRepeatId) {
        LambdaQueryWrapper<PecRepeatRequestDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PecRepeatRequestDetail::getCheckRepeatId, checkRepeatId);
        List<PecRepeatRequestDetail> list = list(wrapper);
        for (PecRepeatRequestDetail detail : list) {
            try {
                if (StringUtils.isNotBlank(detail.getImageCompareJson())) {
                    byte[] compressedData = Base64.getDecoder().decode(detail.getImageCompareJson());
                    detail.setImageCompareJson(CompressionUtil.decompress(compressedData));
                }
                if (StringUtils.isNotBlank(detail.getImageOriginJson())) {
                    byte[] compressedData = Base64.getDecoder().decode(detail.getImageOriginJson());
                    detail.setImageOriginJson(CompressionUtil.decompress(compressedData));
                }
            } catch (IOException e) {
                log.error("压缩字段失败", e);
            }
        }
        return list;
    }

    public void update(String detailId, PecRepeatRequestDetail pecRepeatRequestDetail) {
        try {
            if (StringUtils.isNotBlank(pecRepeatRequestDetail.getImageCompareJson())) {
                byte[] compressedData = CompressionUtil.compress(pecRepeatRequestDetail.getImageCompareJson());
                pecRepeatRequestDetail.setImageCompareJson(Base64.getEncoder().encodeToString(compressedData));
            }
            if (StringUtils.isNotBlank(pecRepeatRequestDetail.getImageOriginJson())) {
                byte[] compressedData = CompressionUtil.compress(pecRepeatRequestDetail.getImageOriginJson());
                pecRepeatRequestDetail.setImageOriginJson(Base64.getEncoder().encodeToString(compressedData));
            }
            LambdaQueryWrapper<PecRepeatRequestDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PecRepeatRequestDetail::getDetailId, detailId);
            update(pecRepeatRequestDetail, wrapper);
        } catch (IOException e) {
            log.error("压缩字段失败, detailId: {}", e);
            throw new RuntimeException("更新时压缩字段失败", e);
        }
    }

}

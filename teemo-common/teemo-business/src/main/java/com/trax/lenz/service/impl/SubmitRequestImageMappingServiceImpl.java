package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trax.lenz.entity.SubmitRequestImageMapping;
import com.trax.lenz.mapper.SubmitRequestImageMappingMapper;
import com.trax.lenz.service.SubmitRequestImageMappingService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 客户图片关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
@Service
public class SubmitRequestImageMappingServiceImpl extends ServiceImpl<SubmitRequestImageMappingMapper, SubmitRequestImageMapping> implements SubmitRequestImageMappingService {

}

package com.trax.lenz.enums;

import lombok.Getter;

/**
 * @ClassName IdentifyStatus
 * @Description:
 * <AUTHOR>
 * @Date 2024-10-28-10:43
 **/
@Getter
public enum IdentifyStatus {

    INIT(1, "待识别"),
    RECOGNITION_ING(2, "识别中"),
    RECOGNITION_FINISH(3, "识别完成"),
    BI_ERROR(4, "bi计算出错"),
    ;

    private final Integer code;

    private final String desc;

    IdentifyStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}

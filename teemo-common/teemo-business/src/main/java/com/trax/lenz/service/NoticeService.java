package com.trax.lenz.service;

import com.google.common.collect.Lists;
import com.trax.lenz.common.utils.FeiShuUtils;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.config.prop.FeiShuProperties;
import com.trax.lenz.dto.feishu.InteractiveMsgResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


/**
 * 通知Service
 *
 * <AUTHOR>
 * @date 2020-05-21 15:32:58
 */
@Service
@Slf4j
public class NoticeService {

    private final FeiShuProperties feiShuProperties;

    @Value("${spring.profiles.active:''}")
    private String active;

    public NoticeService(FeiShuProperties feiShuProperties) {
        this.feiShuProperties = feiShuProperties;
    }

    /**
     * 飞书消息发送
     *
     * @param responseName
     * @param messageMap
     */
    @Async(value = "businessExecutor")
    public void sendFeiShuMessage(String responseName, Map<String, String> messageMap, String color, String sendRobotUrl) {
        StringBuilder stringBuffer = new StringBuilder();
        stringBuffer.append("**环境**: ").append(active).append(" \n");
        for (String key : messageMap.keySet()) {
            stringBuffer.append("**").append(key).append("**: ").append(messageMap.get(key)).append(" \n");
        }
        String msg = stringBuffer.toString();
        InteractiveMsgResp interactiveMsgResp = packageMessage(msg, responseName, color);
        log.info("报警信息内容:{}", JsonUtil.toJsonString(interactiveMsgResp));
        FeiShuUtils.sendInteractiveMessage(sendRobotUrl, JsonUtil.toJsonString(interactiveMsgResp));
    }

    /**
     * 此处报警为调用客户成功, 但是客户返回code为失败, 也判定为失败并报警
     *
     * @param responseId
     * @param customerResponse
     * @param callbackUrl
     */
    @Async(value = "businessExecutor")
    public void responseCallbackFailWarning(String responseId, String taskId, String taskName, String customerResponse, String callbackUrl) {
        log.info("接收到回调客户返回异常告警, responseId:{}", responseId);
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("**环境**: " + active + " \n");
        stringBuffer.append("**任务ID**: " + taskId + " \n");
        stringBuffer.append("**任务名称**: " + taskName + " \n");
        stringBuffer.append("**答卷ID**: " + responseId + " \n");
        stringBuffer.append("**回调地址**: " + callbackUrl + "\n");
        stringBuffer.append("**响应消息**: " + customerResponse + "\n");
        String msg = stringBuffer.toString();
        InteractiveMsgResp interactiveMsgResp = this.packageMessage(msg, "【TEEMO】答卷回调客户返回失败，请相关人员注意！", "red");
        log.info("报警信息内容:{}", JsonUtil.toJsonString(interactiveMsgResp));
        FeiShuUtils.sendInteractiveMessage(feiShuProperties.getCallbackFailRobotUrl(), JsonUtil.toJsonString(interactiveMsgResp));
    }

    /**
     * 此处为回调HTTP响应码异常, 需报警提示
     *
     * @param responseId
     * @param httpCode
     * @param callbackUrl
     * @param errMsg
     */
    @Async(value = "businessExecutor")
    public void customerCallbackFailWarning(String responseId, String taskId, String taskName, String httpCode, String callbackUrl, String errMsg) {
        log.info("接收到回调HTTP相应码异常告警, responseId:{}", responseId);
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("**环境**: " + active+ " \n");
        stringBuffer.append("**任务ID**: " + taskId + " \n");
        stringBuffer.append("**任务名称**: " + taskName + " \n");
        stringBuffer.append("**答卷ID**: " + responseId+ " \n");
        stringBuffer.append("**回调地址**: " + callbackUrl + "\n");
        stringBuffer.append("**http响应码**: " + httpCode  + "\n");
        stringBuffer.append("**异常消息**: " + errMsg  + "\n");
        String msg = stringBuffer.toString();
        InteractiveMsgResp interactiveMsgResp = this.packageMessage(msg, "【TEEMO】答卷回调HTTP响应码异常，请相关人员注意！", "red");
        log.info("报警信息内容:{}", JsonUtil.toJsonString(interactiveMsgResp));
        FeiShuUtils.sendInteractiveMessage(feiShuProperties.getCallbackFailRobotUrl(), JsonUtil.toJsonString(interactiveMsgResp));
    }

    /**
     * 组装飞书卡片数据
     *
     * @param msg
     * @return
     */
    public InteractiveMsgResp packageMessage(String msg, String content, String color) {
        InteractiveMsgResp resp = new InteractiveMsgResp();
        InteractiveMsgResp.Header header = new InteractiveMsgResp.Header();
        // 卡片标头
        header.setTemplate(color);
        InteractiveMsgResp.Title title = new InteractiveMsgResp.Title();
        title.setContent(content);
        title.setTag("plain_text");
        header.setTitle(title);

        InteractiveMsgResp.Config config = new InteractiveMsgResp.Config();
        config.setWide_screen_mode(true);
        config.setUpdate_multi(true);
        config.setEnable_forward(true);

        InteractiveMsgResp.Element element = new InteractiveMsgResp.Element();
        element.setTag("div");

        InteractiveMsgResp.Text text = new InteractiveMsgResp.Text();
        text.setContent(msg);
        text.setTag("lark_md");
        element.setText(text);

        InteractiveMsgResp.Card card = new InteractiveMsgResp.Card();
        card.setHeader(header);
        List<InteractiveMsgResp.Element> elements = Lists.newArrayList();
        elements.add(element);
        card.setElements(elements);
        card.setConfig(config);

        resp.setCard(card);
        resp.setMsg_type("interactive");
        return resp;
    }
}

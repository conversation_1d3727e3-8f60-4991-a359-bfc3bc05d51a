package com.trax.lenz.service;

import com.google.common.collect.Maps;
import com.trax.lenz.api.dto.response.GetAiResultLatestResp;
import com.trax.lenz.common.constants.AppConstants;
import com.trax.lenz.common.utils.HttpUtil;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.dto.HttpResponse;
import com.trax.lenz.dto.request.IdentifyReq;
import com.trax.lenz.dto.response.CallbackFunctionResp;
import com.trax.lenz.entity.IdentifyRequest;
import com.trax.lenz.entity.SubmitRequest;
import com.trax.lenz.entity.SubmitRequestCallback;
import com.trax.lenz.entity.TaskConfig;
import com.trax.lenz.enums.SubmitType;
import com.trax.lenz.paladin.domain.response.GetTaskConfigForTeemoResp;
import com.trax.lenz.service.enterprise.BaseService;
import com.trax.lenz.service.impl.IdentifyRequestServiceImpl;
import com.trax.lenz.service.impl.SubmitRequestCallbackServiceImpl;
import com.trax.lenz.service.impl.SubmitRequestServiceImpl;
import com.trax.lenz.service.impl.TaskConfigServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.ssssssss.magicapi.core.service.MagicAPIService;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 回调service
 *
 * @author: yuyang
 * @date: 2022-08-31 15:50
 */
@Slf4j
@Service
public class CallbackService {

    private SubmitRequestServiceImpl submitRequestService;

    private MagicAPIService magicAPIService;

    private PaladinService paladinService;

    private NoticeService noticeService;

    private IdentifyService identifyService;

    private SubmitRequestCallbackServiceImpl submitRequestCallbackService;

    private TaskConfigServiceImpl taskConfigService;

    private FunctionService functionService;

    private IdentifyRequestServiceImpl identifyRequestService;

    private List<BaseService> baseService;

    public CallbackService(SubmitRequestServiceImpl submitRequestService, MagicAPIService magicAPIService, PaladinService paladinService, NoticeService noticeService, IdentifyService identifyService, SubmitRequestCallbackServiceImpl submitRequestCallbackService, TaskConfigServiceImpl taskConfigService, FunctionService functionService, IdentifyRequestServiceImpl identifyRequestService, List<BaseService> baseService) {
        this.submitRequestService = submitRequestService;
        this.magicAPIService = magicAPIService;
        this.paladinService = paladinService;
        this.noticeService = noticeService;
        this.identifyService = identifyService;
        this.submitRequestCallbackService = submitRequestCallbackService;
        this.taskConfigService = taskConfigService;
        this.functionService = functionService;
        this.identifyRequestService = identifyRequestService;
        this.baseService = baseService;
    }

    /**
     * 新-接受api回调结果之后,直接交由线程回调函数处理
     *
     * @param resp
     */
    @Async(value = "businessExecutor")
    public void resultCallback(GetAiResultLatestResp resp) {
        log.info("接受到API回调请求! resp:{}", resp);
        String responseId = resp.getResponseId();

        // 2023-03-09 by yuyang, 最新的回调方式，先查询新表
        IdentifyRequest identifyRequest = identifyRequestService.getOne(responseId);
        if (identifyRequest != null) {
            // 修改状态
            identifyRequest.setStatus(3);
            identifyRequest.setApiRespContent(JsonUtil.toJsonString(resp));
            identifyRequest.setIdentifyFinishTime(new Date());
            identifyRequestService.update(responseId, identifyRequest);
            log.info("按照V3方式进行回调！调用前端回调方法！responseId={}", responseId);
            TaskConfig taskConfig = taskConfigService.getOne(identifyRequest.getTaskId(), SubmitType.AI_IDENTIFY.getCode());
            String callbackMethod = "";
            if (taskConfig != null) {
                callbackMethod = taskConfig.getCallbackMethod();
            } else {
                try {
                    // 如果查询不到任务配置, 走定制回调
                    log.info("未查询到任务配置，走定制回调！responseId={}", responseId);
                    IdentifyReq identifyReq = JsonUtil.jsonToPojo(identifyRequest.getApiReqContent(), IdentifyReq.class);
                    String customerId = identifyReq.getCustomerId();
                    for (BaseService service : baseService) {
                        if (service.isSupport(customerId)) {
                            callbackMethod = service.getCallbackMethod(responseId, resp);
                            break;
                        }
                    }
                } catch (Exception e) {
                    log.error("定制回调异常！responseId={}", responseId, e);
                }
            }
            callback(responseId, callbackMethod);
            return;
        }

        SubmitRequest request = submitRequestService.getOne(responseId);
        if (1 == request.getTest()) {
            log.info("当前返回答卷为自动化提交，不需要回调客户! responseId:{}", responseId);
            return;
        }
        GetTaskConfigForTeemoResp taskTeemoConfig = paladinService.getTaskTeemoConfig(request.getTaskId());
        if (Optional.ofNullable(taskTeemoConfig).isPresent()) {
            log.info("当前回调任务RPC查询到相应配置，走新回调流程！ responseId:{}", responseId);
            // 新回调V2，增加告警
            callbackV2(resp, taskTeemoConfig);
            return;
        }
        // 调用答卷对应项目的回调接口, 切记要在定制接口中要更新回调状态
        SubmitRequest submitRequest = submitRequestService.getOne(responseId);
        String callbackUrl = submitRequest.getCallbackUrl();
        if (StringUtils.isBlank(callbackUrl)) {
            log.info("回调地址为空！项目可能不需要回调！responseId={}", responseId);
            submitRequestService.updateCallbackStatusFinish(responseId);
            return;
        }
        // 原始回调，兼容历史项目
        callbackV1(resp, callbackUrl);
    }

    private void callback(String responseId, String callbackMethod) {
        Map<String, Object> params = new HashMap<>();
        params.put("responseId", responseId);
        functionService.execMethod(callbackMethod, params);
    }


    /**
     * 新-通用带告警回调方法
     *
     * @param resp
     * @param taskTeemoConfig
     */
    public void callbackV2(GetAiResultLatestResp resp, GetTaskConfigForTeemoResp taskTeemoConfig) {
        String responseId = resp.getResponseId();
        String callbackParam = "";
        // 获取提交流水
        if (Boolean.FALSE.equals(taskTeemoConfig.getCallbackEnable())) {
            log.info("未查询到当前responseId任务配置或当前项目回调开关为关闭状态, 回调退出！ responseId:{}", responseId);
            // 更新回调状态
            submitRequestService.updateCallbackStatusFinish(responseId);
            return;
        }
        // 获取任务配置回调地址
        String callbackUrl = taskTeemoConfig.getCallbackUrl();
        // 调用前台项目回调函数
        String callbackResp = callbackV1(resp, taskTeemoConfig.getCallbackFunctionPath());
        if (!Optional.ofNullable(callbackResp).isPresent()) {
            log.info("回调函数返回结果为空，放弃回调客户！responseId:{}", responseId);
            return;
        }
        log.info("回调函数返回, responseId:{}, 结果:{}", responseId, JsonUtil.toJsonString(callbackResp));
        CallbackFunctionResp functionResp = JsonUtil.jsonToPojo(callbackResp, CallbackFunctionResp.class);

        log.info("接收到前端回调函数返回! responseId:{}, 返回回调参数:{}, 请求方式:{}, 回调地址:{}", responseId, functionResp.getSearchResult(), functionResp.getMethod(), taskTeemoConfig.getCallbackUrl());
        HttpResponse httpResponse = new HttpResponse();
        boolean flag = false;
        if (AppConstants.CALLBACK_POST_JSON.equals(functionResp.getMethod())) {
            httpResponse = HttpUtil.post(callbackUrl, functionResp.getSearchResult(), null);
            callbackParam = functionResp.getSearchResult();
        }
        if (AppConstants.CALLBACK_POST_FORM.equals(functionResp.getMethod())) {
            httpResponse = HttpUtil.postForMap(callbackUrl, functionResp.getParamMap(), null);
            callbackParam = JsonUtil.toJsonString(functionResp.getParamMap());
        }
        log.info("回调【{}】项目, responseId:{}, 回调返回结果:{}", taskTeemoConfig.getTaskName(), responseId, JsonUtil.toJsonString(httpResponse.getResponse()));
        if (StringUtils.isNotEmpty(httpResponse.getCode())) {
            if (!AppConstants.HTTP_SUCCESS_CODE.equals(httpResponse.getCode())) {
                // HTTP状态码异常告警
                noticeService.customerCallbackFailWarning(responseId, taskTeemoConfig.getTaskId(), taskTeemoConfig.getTaskName(), httpResponse.getCode(), callbackUrl, httpResponse.getResponse());
            } else {
                // 回调客户返回不为空, 并且状态为200, 走验证
                log.info("开始调用前台函数进行客户回调结果验证, responseId:{}", responseId);
                flag = isCallbackFlag(taskTeemoConfig, httpResponse);
                log.info("当前回调客户responseId:{}, 最终回调状态:【{}】", responseId, flag);
                if (!flag) {
                    // 此处报警为调用成功, 但是客户返回code为失败, 也判定为失败并报警
                    noticeService.responseCallbackFailWarning(responseId, taskTeemoConfig.getTaskId(), taskTeemoConfig.getTaskName(), JsonUtil.toJsonString(httpResponse.getResponse()), callbackUrl);
                }
            }
        } else {
            // 此处为POST请求异常, 报警提示
            noticeService.customerCallbackFailWarning(responseId, taskTeemoConfig.getTaskId(), taskTeemoConfig.getTaskName(), "", callbackUrl, httpResponse.getResponse());
        }

        // 保存回调数据
        SubmitRequestCallback callback = new SubmitRequestCallback();
        callback.setResponseId(responseId);
        callback.setCallbackUrl(callbackUrl);
        callback.setCallbackType(functionResp.getMethod());
        callback.setCallbackParam(callbackParam);
        callback.setCallbackHttpCode(httpResponse.getCode());
        callback.setCallbackResponse(httpResponse.getResponse());
        callback.setStatus(flag ? 1 : 2);
        callback.setCounts(1);
        callback.setProcessNo(submitRequestCallbackService.getProcessNoByResponseId(responseId));
        identifyService.saveCallback(JsonUtil.toJsonString(callback));

        // 修改请求流水状态
        identifyService.updateCallbackStatusFinish(responseId);
    }

    private boolean isCallbackFlag(GetTaskConfigForTeemoResp taskTeemoConfig, HttpResponse httpResponse) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("customerResponse", httpResponse.getResponse());
        // 调用前台制定项目回调函数进行项目回调
        return magicAPIService.invoke(taskTeemoConfig.getIsCallbackSuccessFunctionPath(), params);
    }

    private String callbackV1(GetAiResultLatestResp resp, String callbackUrl) {
        log.info("开始调用前台函数进行客户回调, responseId:{}", resp.getResponseId());
        Map<String, Object> params = new HashMap<>();
        params.put("responseId", resp.getResponseId());
        params.put("customRequestId", resp.getCustomRequestId());
        // 调用前台制定项目回调函数进行项目回调
        return magicAPIService.invoke(callbackUrl, params);
    }

    public void isCallbackSuccess() {
        log.info("开始调用前台函数进行客户回调结果验证, responseId:{}", 111);
        Map<String, Object> params = Maps.newHashMap();
        // 江中一期
//        String text = "{\"success\":true,\"code\":200,\"message\":\"任务接收成功!\",\"data\":null}";
        // 妮维雅
//        String text = "{\"code\":0,\"message\":\"success\"}";
        // gsk
        String text = "{\"resultStatus\":{\"code\":\"0\",\"errMsg\":\"ok\"}}";
        params.put("customerResponse", text);
        // 调用前台制定项目回调函数进行项目回调
        Boolean flag = magicAPIService.invoke("/gsk/isCallbackSuccess", params);
        log.info("flag:{}", flag);
    }
}



package com.trax.lenz.component.sharding;

import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * IdentifyRequestIdMapping表分片实现类
 *
 * @author: yuyang
 * @date: 2022-04-13 14:27
 */
@Slf4j
public class PreciseAlgorithmIdentifyRequestIdMapping implements PreciseShardingAlgorithm<String> {

    public static final int tablesNum = 20;

    @Override
    public String doSharding(Collection<String> tables, PreciseShardingValue<String> preciseShardingValue) {
        int index = Math.abs(preciseShardingValue.getValue().hashCode()) % tablesNum;
        if (index < 10){
            return preciseShardingValue.getLogicTableName() + "_0" + index;
        }
        return preciseShardingValue.getLogicTableName() + "_" + index;
    }

    public static void main(String[] args) {
        Map<Integer, Integer> aa = new HashMap<>();
        for (int i = 1; i < 100; i++){
            int index = Math.abs(UUID.randomUUID().hashCode()) % tablesNum;
            if (aa.containsKey(index)){
                aa.put(index, aa.get(index) + 1);
            } else {
                aa.put(index, 1);
            }
        }
        for(Integer a : aa.keySet()){
            System.out.println("key=" + a + ", value=" + aa.get(a));
        }
    }
}

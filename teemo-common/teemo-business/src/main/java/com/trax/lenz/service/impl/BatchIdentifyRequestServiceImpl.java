package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trax.lenz.entity.BatchIdentifyRequest;
import com.trax.lenz.mapper.BatchIdentifyRequestMapper;
import com.trax.lenz.service.IBatchIdentifyRequestService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 客户批量计算请求记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Service
public class BatchIdentifyRequestServiceImpl extends ServiceImpl<BatchIdentifyRequestMapper, BatchIdentifyRequest> implements IBatchIdentifyRequestService {

    public BatchIdentifyRequest getOne(String responseGroupId) {
        LambdaQueryWrapper<BatchIdentifyRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BatchIdentifyRequest::getResponseGroupId, responseGroupId);
        return getOne(wrapper);
    }

    public void update(String responseGroupId, BatchIdentifyRequest batchIdentifyRequest) {
        LambdaQueryWrapper<BatchIdentifyRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BatchIdentifyRequest::getResponseGroupId, responseGroupId);
        update(batchIdentifyRequest, wrapper);
    }
}

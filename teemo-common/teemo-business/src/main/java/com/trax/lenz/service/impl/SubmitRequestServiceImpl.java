package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trax.lenz.entity.SubmitRequest;
import com.trax.lenz.mapper.SubmitRequestMapper;
import com.trax.lenz.service.SubmitRequestService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 客户识别请求记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Service
public class SubmitRequestServiceImpl extends ServiceImpl<SubmitRequestMapper, SubmitRequest> implements SubmitRequestService {

    public SubmitRequest getOne(String responseId){
        LambdaQueryWrapper<SubmitRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SubmitRequest::getResponseId, responseId);
        return getOne(wrapper);
    }

    public void updateCallbackStatusFinish(String responseId){
        LambdaQueryWrapper<SubmitRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SubmitRequest::getResponseId, responseId);
        SubmitRequest submitRequest = new SubmitRequest();
        submitRequest.setResponseId(responseId);
        submitRequest.setCallbackStatus(1);
        submitRequest.setUpdateTime(new Date());
        update(submitRequest, wrapper);
    }

}

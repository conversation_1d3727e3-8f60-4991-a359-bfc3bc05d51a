package com.trax.lenz.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 客户图片关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-29
 */
@Data
public class SubmitRequestImageMapping {

    private static final long serialVersionUID = 1L;

    /**
     * 图片id
     */
    private String imageId;

    /**
     * 答卷id
     */
    private String responseId;

    /**
     * 题目id
     */
    private String questionId;

    /**
     * 客户图片id
     */
    private String customerImageId;

    /**
     * 客户图片组号
     */
    private String customerImageGroupNo;

    /**
     * 客户图片行编号
     */
    private Integer customerImageRowNo;

    /**
     * 客户图片列编号
     */
    private Integer customerImageColumnNo;

    /**
     * 客户图片名称
     */
    private String customerImageName;

    /**
     * 客户图片类型（1.普通图 2.深度图）
     */
    private Integer customerImageType;

    /**
     * 客户图片url
     */
    private String customerImageUrl;

    /**
     * 客户图片拓展信息
     */
    private String customerImageExpandJson;


}

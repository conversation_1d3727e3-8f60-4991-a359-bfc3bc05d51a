package com.trax.lenz.common.utils;

import com.google.common.collect.Lists;
import com.trax.lenz.dto.feishu.InteractiveMsgResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * 飞书推送消息工具类
 *
 * <AUTHOR>
 * @date 2022-03-18 11:19:59
 */
@Slf4j
public class FeiShuUtils {

    /**
     * 飞书机器人推送消息
     *
     * @param robotUrl 机器人地址
     * @param content  消息内容
     * @return
     */
    public static boolean sendMessage(String robotUrl, String content) {
        if (StringUtils.isBlank(robotUrl)){
            return false;
        }
        HttpClient httpclient = HttpClients.createDefault();
        HttpPost httppost = new HttpPost(robotUrl);
        httppost.addHeader("Content-Type", "application/json; charset=utf-8");
        String textMsg = "{\"msg_type\":\"text\",\"content\":{\"text\":\"" + content + "\"}}";
        StringEntity se = new StringEntity(textMsg, "utf-8");
        httppost.setEntity(se);
        try {
            log.info("飞书发消息请求robotUrl={}, content={}", robotUrl, content);
            HttpResponse response = httpclient.execute(httppost);
            String result = EntityUtils.toString(response.getEntity(), "utf-8");
            log.info("飞书发消息返回值result={}", result);
            JSONObject jsonObject = new JSONObject(result);
            if (jsonObject.getInt("StatusCode") == 0) {
                return true;
            }
        } catch (IOException e) {
            log.error("飞书发消息异常！", e);
        }
        return false;
    }

    /**
     * 飞书机器人推送卡片消息
     *
     * @param robotUrl 机器人地址
     * @param content  消息内容
     * @return
     */
    public static boolean sendInteractiveMessage(String robotUrl, String content) {
        if (StringUtils.isBlank(robotUrl)){
            return false;
        }
        HttpClient httpclient = HttpClients.createDefault();
        HttpPost httppost = new HttpPost(robotUrl.trim());
        httppost.addHeader("Content-Type", "application/json; charset=utf-8");
        StringEntity se = new StringEntity(content, "utf-8");
        httppost.setEntity(se);
        try {
            log.info("飞书发消息卡片请求robotUrl={}, content={}", robotUrl, content);
            HttpResponse response = httpclient.execute(httppost);
            String result = EntityUtils.toString(response.getEntity(), "utf-8");
            log.info("飞书发消息返回值result={}", result);
            JSONObject jsonObject = new JSONObject(result);
            if (jsonObject.getInt("StatusCode") == 0) {
                return true;
            }
        } catch (IOException e) {
            log.error("飞书发消息异常！{}", e.getMessage());
        }
        return false;
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("**【API】AI厂商受理失败，请及时处理！**\n");
        stringBuffer.append("*主机: AI-TX-Gamma-BJ5-Image-01*\n");
        stringBuffer.append("~~主机IP: *******~~" + "\n");
        stringBuffer.append("**环境: TEST**\n");
        stringBuffer.append("**答卷ID: **" + 121212 + " \n");
        log.info("log:{}", stringBuffer.toString());
        InteractiveMsgResp resp = new InteractiveMsgResp();
        InteractiveMsgResp.Header header = new InteractiveMsgResp.Header();
        // 卡片标头
        header.setTemplate("red");
        InteractiveMsgResp.Title title = new InteractiveMsgResp.Title();
        title.setContent("拍拍赚:测试卡片发送消息");
        title.setTag("plain_text");
        header.setTitle(title);

        InteractiveMsgResp.Config config = new InteractiveMsgResp.Config();
        config.setWide_screen_mode(true);
        config.setUpdate_multi(true);
        config.setEnable_forward(true);

        InteractiveMsgResp.Element element = new InteractiveMsgResp.Element();
        element.setTag("div");

        InteractiveMsgResp.Text text = new InteractiveMsgResp.Text();
        text.setContent(stringBuffer.toString());
        text.setTag("lark_md");
        element.setText(text);

        InteractiveMsgResp.Card card = new InteractiveMsgResp.Card();
        card.setHeader(header);
        List<InteractiveMsgResp.Element> elements = Lists.newArrayList();
        elements.add(element);
        card.setElements(elements);
        card.setConfig(config);

        resp.setCard(card);
        resp.setMsg_type("interactive");

        log.info("resp:{}", resp.toString());

        System.out.println(sendInteractiveMessage("https://open.feishu.cn/open-apis/bot/v2/hook/f4d1c277-07b8-408d-aa2d-4ac7729b48d5", JsonUtil.toJsonString(resp)));
    }
}

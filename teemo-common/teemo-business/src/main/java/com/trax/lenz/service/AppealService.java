package com.trax.lenz.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trax.lenz.common.core.domain.R;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.entity.AppealRequest;
import com.trax.lenz.entity.TaskConfig;
import com.trax.lenz.enums.AppealStatus;
import com.trax.lenz.gabriel.dto.request.AddAppealReq;
import com.trax.lenz.gabriel.remote.RemoteAppealClient;
import com.trax.lenz.service.impl.AppealRequestServiceImpl;
import com.trax.lenz.service.impl.TaskConfigServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * AppealService
 *
 * @author: yuyang
 * @date: 2023-03-06
 */
@Slf4j
@Service
public class AppealService {

    private AppealRequestServiceImpl appealRequestService;

    private TaskConfigServiceImpl taskConfigService;

    private FunctionService functionService;

    private RemoteAppealClient remoteAppealClient;

    public AppealService(AppealRequestServiceImpl appealRequestService, TaskConfigServiceImpl taskConfigService, FunctionService functionService, RemoteAppealClient remoteAppealClient) {
        this.appealRequestService = appealRequestService;
        this.taskConfigService = taskConfigService;
        this.functionService = functionService;
        this.remoteAppealClient = remoteAppealClient;
    }

    /**
     * 申诉提交
     *
     * @param req
     * @return
     */
    public void submitAppeal(AddAppealReq req) {
        R r = remoteAppealClient.addAppeal(req);
        log.info("【申诉提交】提交返回值！r={}", JsonUtil.toJsonString(r));
    }

    /**
     * 申诉回调
     *
     * @param req
     * @return
     */
    public void callback(AddAppealReq req) {

        // 修改申诉记录状态
        String appealId = req.getAppealId().toString();
        AppealRequest appealRequest = appealRequestService.getOne(appealId);
        String responseId = appealRequest.getResponseId();
        LambdaQueryWrapper<AppealRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppealRequest::getAppealId, appealId);
        AppealRequest request = new AppealRequest();
        request.setStatus(AppealStatus.APPEAL_FINISH.getCode());
        request.setAuditFinishTime(new Date());
        request.setGabrielRespContent(JsonUtil.toJsonString(req));
        request.setUpdateTime(new Date());
        appealRequestService.update(request, wrapper);
        log.info("【申诉回调】修改状态为申诉完成！appealId={}, responseId={}", appealId, responseId);

        // 查询任务配置
        TaskConfig taskConfig = taskConfigService.getOne(appealRequest.getTaskId(), appealRequest.getType());

        // 执行前端查询方法
        Map<String, Object> params = new HashMap<>();
        params.put("appealId", appealId);
        functionService.execMethod(taskConfig.getCallbackMethod(), params);
    }
}

package com.trax.lenz.component.sharding;

import com.trax.lenz.common.core.id.SnowFlakeFactory;
import com.trax.lenz.common.core.util.SpringUtils;
import com.trax.lenz.common.utils.DateUtil;
import com.trax.lenz.config.prop.ShardingProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;

/**
 * 数据库分库策略
 *
 * @author: yang.yu
 * @date: 2023-06-13
 */
@Slf4j
public class DatabaseShardingAlgorithm implements PreciseShardingAlgorithm<String> {

    private SnowFlakeFactory snowFlakeFactory;

    private ShardingProperties shardingProperties;

    public void init() {
        if (snowFlakeFactory == null) {
            snowFlakeFactory = SpringUtils.getBean(SnowFlakeFactory.class);
        }
        if (shardingProperties == null) {
            shardingProperties = SpringUtils.getBean(ShardingProperties.class);
        }
    }

    /**
     * 分片规则
     *
     * @param tables
     * @param preciseShardingValue
     * @return String
     */
    @Override
    public String doSharding(Collection<String> tables, PreciseShardingValue<String> preciseShardingValue) {
        init();
        String month = snowFlakeFactory.getDateMoth(preciseShardingValue.getValue());
        Date date = DateUtil.convert2Date(month, DateUtil.DATE_FORMAT_21);
        if (StringUtils.isNotBlank(month) && isMonth(month) && DateUtil.addMonths(date, shardingProperties.getMonthNum()).before(new Date())) {
            log.info("本次查询数据源dataSource=ds1【polar】");
            return "ds1";
        }
        log.info("本次查询数据源dataSource=ds0【mysql】");
        return "ds0";
    }

    private static boolean isMonth(String month) {
        try {
            SimpleDateFormat simple = new SimpleDateFormat(DateUtil.DATE_FORMAT_21);
            simple.setLenient(false);
            simple.parse(month);
        } catch (Exception e) {
            return false;
        }
        return true;
    }
}

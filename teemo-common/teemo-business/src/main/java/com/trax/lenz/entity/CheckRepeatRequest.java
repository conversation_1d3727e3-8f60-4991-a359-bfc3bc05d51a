package com.trax.lenz.entity;

import lombok.Data;

import java.util.Date;

/**
 * 查重请求
 *
 * @author: yuy<PERSON>
 * @date：2024-01-24
 */
@Data
public class CheckRepeatRequest {
    private String checkRepeatId;
    private String responseId;
    private String customerRequestId;
    private String taskId;
    private String taskName;
    private Integer type;
    private Integer status;
    private Date createTime;
    private Integer test;
    private String reqContent;
    private String kylinReqContent;
    private Date finishTime;
    private String kylinRespContent;
    private Integer callbackStatus;
    private Integer counts;
    private Integer retryEnabled;
    private String callbackUrl;
    private String callbackType;
    private String callbackParam;
    private String callbackHttpCode;
    private String callbackResponse;
    private Integer imageNum;
    private String repeatId;
    private Date updateTime;
}

package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trax.lenz.entity.IdentifyRequestDetail;
import com.trax.lenz.mapper.IdentifyRequestDetailMapper;
import com.trax.lenz.service.IIdentifyRequestDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 识别请求记录子能力明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Service
public class IdentifyRequestDetailServiceImpl extends ServiceImpl<IdentifyRequestDetailMapper, IdentifyRequestDetail> implements IIdentifyRequestDetailService {

    public boolean isFinish(String responseId) {
        LambdaQueryWrapper<IdentifyRequestDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IdentifyRequestDetail::getResponseId, responseId);
        wrapper.ne(IdentifyRequestDetail::getStatus, 3);
        return count(wrapper) == 0;
    }

    public List<IdentifyRequestDetail> getList(String responseId) {
        LambdaQueryWrapper<IdentifyRequestDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IdentifyRequestDetail::getResponseId, responseId);
        return list(wrapper);
    }

    public IdentifyRequestDetail getOneByResponseIdAndChildId(String responseId, String childId) {
        LambdaQueryWrapper<IdentifyRequestDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IdentifyRequestDetail::getResponseId, responseId);
        wrapper.eq(IdentifyRequestDetail::getChildId, childId);
        return getOne(wrapper);
    }

    public void update(String responseId, String childId, IdentifyRequestDetail oneByChildDetail) {
        LambdaQueryWrapper<IdentifyRequestDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IdentifyRequestDetail::getResponseId, responseId);
        wrapper.eq(IdentifyRequestDetail::getChildId, childId);
        update(oneByChildDetail, wrapper);
    }
}

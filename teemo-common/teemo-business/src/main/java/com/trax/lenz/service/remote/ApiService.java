package com.trax.lenz.service.remote;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.trax.lenz.api.dto.request.GetAiResultLatestReq;
import com.trax.lenz.api.dto.request.GetResponseResultBaseReq;
import com.trax.lenz.api.dto.response.GenerateResponseIdResp;
import com.trax.lenz.api.dto.response.GetAiResultLatestResp;
import com.trax.lenz.api.dto.response.GetImageListResp;
import com.trax.lenz.api.dto.response.GetStitchImageSourceImageResp;
import com.trax.lenz.api.dto.response.IdentifyFinishResp;
import com.trax.lenz.api.dto.response.RetCode;
import com.trax.lenz.api.dto.response.SubmitResp;
import com.trax.lenz.api.dto.submit.InnerSubmitReq;
import com.trax.lenz.api.dto.submit.InnerSubmitV2Req;
import com.trax.lenz.api.dto.submit.InnerSubmitV2Resp;
import com.trax.lenz.api.remote.ApiManagerFeignClient;
import com.trax.lenz.api.remote.ApiServiceFeignClient;
import com.trax.lenz.api.remote.ApiServiceFeignClientV2;
import com.trax.lenz.common.core.domain.R;
import com.trax.lenz.common.core.exception.BusinessException;
import com.trax.lenz.common.core.id.SnowFlakeFactory;
import com.trax.lenz.common.utils.CglibCopyBeanUtil;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.dto.request.IdentifyReq;
import com.trax.lenz.dto.request.IdentifyReqCoordinate;
import com.trax.lenz.dto.request.IdentifyReqExtend;
import com.trax.lenz.dto.request.IdentifyReqGroup;
import com.trax.lenz.dto.request.IdentifyReqImage;
import com.trax.lenz.dto.request.IdentifyV2Req;
import com.trax.lenz.dto.response.CustomerImageRelationResp;
import com.trax.lenz.entity.IdentifyRequest;
import com.trax.lenz.entity.SubmitRequest;
import com.trax.lenz.service.PaladinService;
import com.trax.lenz.service.impl.IdentifyRequestServiceImpl;
import com.trax.lenz.service.impl.SubmitRequestServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * ApiService
 *
 * @author: yuyang
 * @date: 2023-03-06 15:50
 */
@Slf4j
@Service
public class ApiService {

    private final SubmitRequestServiceImpl submitRequestService;

    private final SnowFlakeFactory snowFlakeFactory;

    private final ApiServiceFeignClient apiServiceFeignClient;

    private final ApiManagerFeignClient apiManagerFeignClient;

    private final PaladinService paladinService;

    private final ApiServiceFeignClientV2 apiServiceFeignClientV2;

    private final IdentifyRequestServiceImpl identifyRequestService;

    public ApiService(SubmitRequestServiceImpl submitRequestService, SnowFlakeFactory snowFlakeFactory, ApiServiceFeignClient apiServiceFeignClient, ApiManagerFeignClient apiManagerFeignClient, PaladinService paladinService, ApiServiceFeignClientV2 apiServiceFeignClientV2, IdentifyRequestServiceImpl identifyRequestService) {
        this.submitRequestService = submitRequestService;
        this.snowFlakeFactory = snowFlakeFactory;
        this.apiServiceFeignClient = apiServiceFeignClient;
        this.apiManagerFeignClient = apiManagerFeignClient;
        this.paladinService = paladinService;
        this.apiServiceFeignClientV2 = apiServiceFeignClientV2;
        this.identifyRequestService = identifyRequestService;
    }

    /**
     * 生成ResponseId
     *
     * @return String
     */
    public String generateResponseId() {
        try {
            R<GenerateResponseIdResp> respR = apiServiceFeignClient.generateResponseId();
            return respR.getData().getResponseId();
        } catch (Exception e) {
            log.error("调用API生成答卷id异常！", e);
        }
        return snowFlakeFactory.nextIdStr();
    }

    /**
     * 提交识别
     *
     * @param identifyReqJson
     * @return
     */
    public void submit(String responseId, String identifyReqJson) {
        IdentifyReq identifyReq = JsonUtil.jsonToPojo(identifyReqJson, IdentifyReq.class);

        // 组装api请求对象
        InnerSubmitReq innerSubmitReq = new InnerSubmitReq();
        innerSubmitReq.setTaskId(identifyReq.getTaskId());
        innerSubmitReq.setResponseId(responseId);
        innerSubmitReq.setCustomerId(identifyReq.getCustomerId());
        innerSubmitReq.setRepeatSubmit(identifyReq.getRepeatSubmit());
        if (Optional.ofNullable(identifyReq.getCustomerRequestId()).isPresent()) {
            innerSubmitReq.setCustomerRequestId(identifyReq.getCustomerRequestId());
        }
        innerSubmitReq.setExtendInfoJson("");
        if (Optional.ofNullable(identifyReq.getExtendInfoJson()).isPresent()) {
            innerSubmitReq.setExtendInfoJson(identifyReq.getExtendInfoJson());
        }

        IdentifyReqExtend extendInfo = identifyReq.getExtendInfo();
        InnerSubmitReq.MetaData responseExtendInfo = new InnerSubmitReq.MetaData();
        CglibCopyBeanUtil.basicCopyBean(extendInfo, responseExtendInfo);
        innerSubmitReq.setMetaData(responseExtendInfo);
        innerSubmitReq.setSubmitMode(IdentifyReq.SUBMIT_MODE_INNER_TEEMO);

        List<InnerSubmitReq.Group> groupList = new ArrayList<>();

        // 校验拼接参数
        checkCoordinate(identifyReq.getGroupList());

        for (IdentifyReqGroup group : identifyReq.getGroupList()) {
            InnerSubmitReq.Group api_group = new InnerSubmitReq.Group();
            api_group.setGroupId(Integer.valueOf(group.getGroupId()));
            if (Optional.ofNullable(group.getCustomerImageGroupNo()).isPresent()) {
                api_group.setCustomerImageGroupNo(group.getCustomerImageGroupNo());
            }
            if (Optional.ofNullable(group.getStitchOrientation()).isPresent()) {
                api_group.setStitchOrientation(group.getStitchOrientation());
            }
            api_group.setQuestionId(group.getQuestionId());

            List<InnerSubmitReq.Group.Image> imageList = new ArrayList<>();
            for (IdentifyReqImage image : group.getImageList()) {
                // 组装api对象
                InnerSubmitReq.Group.Image api_image = new InnerSubmitReq.Group.Image();
                api_image.setImageId(image.getImageId());
                api_image.setImageName(image.getImageName());
                api_image.setImageUrl(image.getImageUrl());
                api_image.setRowNo(image.getRowNo());
                api_image.setColumnNo(image.getColumnNo());
                api_image.setImageType(image.getImageType());
                imageList.add(api_image);
            }
            api_group.setImageList(imageList);
            groupList.add(api_group);
        }
        innerSubmitReq.setGroupList(groupList);

        // 提交api进行识别
        log.info("提交报文：innerSubmitReq={}", JsonUtil.toJsonString(innerSubmitReq));
        R<SubmitResp> respR = apiServiceFeignClient.submit(innerSubmitReq);
        log.info("【teemo提交】识别厂商返回值：responseId={}, returnJson={}", responseId, JsonUtil.toJsonString(respR));
        if (respR.isOk() && respR.getData().isSuccess()) {
            log.info("【teemo提交】提交成功！responseId:{}", responseId);
            LambdaQueryWrapper<SubmitRequest> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SubmitRequest::getResponseId, responseId);
            SubmitRequest request = new SubmitRequest();
            request.setStatus(2);
            request.setUpdateTime(new Date());
            submitRequestService.update(request, wrapper);
        } else {
            log.info("【teemo提交】提交失败！responseId:{}", responseId);
        }
        if (999 == respR.getCode()) {
            log.info("答卷提交API失败!, 错误码:{}", respR.getMsg());
            throw new BusinessException(RetCode.getDesc(respR.getMsg()), respR.getCode());
        }
    }

    /**
     * 提交识别V2
     *
     * @param identifyReqJson
     * @return
     */
    public InnerSubmitV2Resp submitV2(String responseId, String identifyReqJson) {
        IdentifyV2Req identifyReq = JsonUtil.jsonToPojo(identifyReqJson, IdentifyV2Req.class);
        // 组装api请求对象
        InnerSubmitV2Req innerSubmitV2Req = new InnerSubmitV2Req();
        innerSubmitV2Req.setTaskId(identifyReq.getTaskId());
        innerSubmitV2Req.setResponseId(responseId);
        innerSubmitV2Req.setCustomerId(identifyReq.getCustomerId());
        if (Optional.ofNullable(identifyReq.getCustomerRequestId()).isPresent()) {
            innerSubmitV2Req.setCustomerRequestId(identifyReq.getCustomerRequestId());
        }
        innerSubmitV2Req.setRepeatSubmit(identifyReq.getRepeatSubmit());
        IdentifyReqExtend extendInfo = identifyReq.getExtendInfo();
        InnerSubmitV2Req.MetaData responseExtendInfo = new InnerSubmitV2Req.MetaData();
        CglibCopyBeanUtil.basicCopyBean(extendInfo, responseExtendInfo);
        innerSubmitV2Req.setMetaData(responseExtendInfo);
        innerSubmitV2Req.setTest(identifyReq.getTest());
        innerSubmitV2Req.setSubmitMode(IdentifyV2Req.SUBMIT_MODE_INNER_TEEMO);
        innerSubmitV2Req.setType(identifyReq.getType());
        innerSubmitV2Req.setImageId(identifyReq.getImageId());
        innerSubmitV2Req.setImageUrl(identifyReq.getImageUrl());
        innerSubmitV2Req.setBusinessName(identifyReq.getBusinessName());
        innerSubmitV2Req.setModelName(identifyReq.getModelName());
        // 提交api进行识别
        log.info("提交报文：innerSubmitV2Req={}", JsonUtil.toJsonString(innerSubmitV2Req));
        R<InnerSubmitV2Resp> respR = apiServiceFeignClientV2.urlSubmitV2(innerSubmitV2Req);
        log.info("【teemo提交】识别厂商返回值：responseId={}, returnJson={}", responseId, JsonUtil.toJsonString(respR));
        if (respR.isOk()) {
            log.info("【teemo提交】提交成功！responseId:{}", responseId);
            IdentifyRequest identifyRequest = new IdentifyRequest();
            identifyRequest.setStatus(2);
            identifyRequest.setApiReqContent(JsonUtil.toJsonString(innerSubmitV2Req));
            identifyRequest.setApiRespContent(JsonUtil.toJsonString(respR.getData()));
            identifyRequestService.update(responseId, identifyRequest);
            return respR.getData();
        } else {
            log.info("【teemo提交】提交失败！responseId:{}", responseId);
        }
        if (999 == respR.getCode()) {
            log.info("答卷提交API失败!, 错误码:{}", respR.getMsg());
            throw new BusinessException(RetCode.getDesc(respR.getMsg()), respR.getCode());
        }
        return null;
    }

    /**
     * 提交校验拼接坐标数据是否合规
     *
     * @param groupList
     */
    private void checkCoordinate(List<IdentifyReqGroup> groupList) {
        List<IdentifyReqCoordinate> identifyReqCoordinateList = Lists.newArrayList();
        for (IdentifyReqGroup reqGroup : groupList) {
            if (Optional.ofNullable(reqGroup.getImageList()).isPresent() && paladinService.isStitchQuestion(reqGroup.getQuestionId())) {
                List<IdentifyReqImage> imageList = reqGroup.getImageList();
                IdentifyReqCoordinate reqCoordinate = new IdentifyReqCoordinate();
                reqCoordinate.setQuestionId(reqGroup.getQuestionId());
                List<IdentifyReqCoordinate.Coordinate> coordinateList = Lists.newArrayList();
                for (IdentifyReqImage image : imageList) {
                    IdentifyReqCoordinate.Coordinate coordinate = new IdentifyReqCoordinate.Coordinate();
                    coordinate.setRowNo(image.getRowNo());
                    coordinate.setColumnNo(image.getColumnNo());
                    coordinateList.add(coordinate);
                }
                reqCoordinate.setCoordinateList(coordinateList);
                identifyReqCoordinateList.add(reqCoordinate);
            }
        }

        for (IdentifyReqCoordinate reqCoordinate : identifyReqCoordinateList) {
            Integer oneSize = 0;
            List<IdentifyReqCoordinate.Coordinate> coordinateList = reqCoordinate.getCoordinateList();
            Map<Integer, List<IdentifyReqCoordinate.Coordinate>> rowNoMap = coordinateList.stream().collect(Collectors.groupingBy(IdentifyReqCoordinate.Coordinate::getRowNo));
            for (Integer rowNo : rowNoMap.keySet()) {
                List<IdentifyReqCoordinate.Coordinate> columnNoDescList = rowNoMap.get(rowNo).stream().sorted(Comparator.comparing(IdentifyReqCoordinate.Coordinate::getColumnNo).reversed()).collect(Collectors.toList());
                int columnSize = columnNoDescList.size();
                if (0 == oneSize) {
                    oneSize = columnSize;
                } else {
                    if (!oneSize.equals(columnSize)) {
                        throw new BusinessException("设置相邻组列数异常, 请核对后重新上传! 问题题目Id:" + reqCoordinate.getQuestionId(), 999);
                    }
                }
                for (IdentifyReqCoordinate.Coordinate coordinate : columnNoDescList) {
                    if (!coordinate.getColumnNo().equals(columnSize)) {
                        throw new BusinessException("设置拼接参数异常, 请核对后重新上传! 问题题目Id:" + reqCoordinate.getQuestionId(), 999);
                    }
                    columnSize--;
                }
            }
        }
    }

    /**
     * 判断是否识别完成
     *
     * @param responseId
     * @return
     */
    public boolean isFinish(String responseId) {
        try {
            R<IdentifyFinishResp> respR = apiServiceFeignClient.isFinish(responseId);
            if (respR.isOk() && respR.getData().isFinish()) {
                return true;
            }
        } catch (Exception e) {
            log.error("RPC查询答卷识别状态异常！responseId={}", responseId);
        }
        return false;
    }

    /**
     * 查询图片之间的关系
     *
     * @param responseId
     * @return Map<String, CustomerImageRelationResp>
     */
    public Map<String, CustomerImageRelationResp> getImageMap(String responseId) {
        List<CustomerImageRelationResp> imageList = Lists.newArrayList();
        try {
            log.info("RPC查询答卷图片列表请求参数responseId={}", responseId);
            R<GetImageListResp> respR = apiServiceFeignClient.getImageList(responseId);
            log.info("RPC查询答卷图片列表返回结果:{}", JsonUtil.toJsonString(respR));
            if (respR.isOk()) {
                List<GetImageListResp.Image> list = respR.getData().getList();
                for (GetImageListResp.Image image : list) {
                    CustomerImageRelationResp imageRelationResp = new CustomerImageRelationResp();
                    imageRelationResp.setImageId(image.getImageId());
                    imageRelationResp.setCustomerImageId(image.getCustomerImageId());
                    imageRelationResp.setCustomerImageName(image.getCustomerImageName());
                    imageRelationResp.setCustomerImageUrl(image.getCustomerImageUrl());
                    imageRelationResp.setImageServerPath(image.getImageServerPath());
                    imageRelationResp.setGroupNo(image.getGroupNo());
                    imageRelationResp.setCustomerImageGroupNo(image.getCustomerImageGroupNo());
                    imageList.add(imageRelationResp);
                }
            }
        } catch (Exception e) {
            log.error("RPC查询答卷图片列表异常！responseId={}", responseId);
        }
        if (CollectionUtils.isEmpty(imageList)) {
            return Maps.newHashMap();
        }
        return imageList.stream().collect(Collectors.toMap(CustomerImageRelationResp::getImageId, customerImageRelationResp -> customerImageRelationResp));
    }

    /**
     * 获取AI最后一次识别结果文件
     *
     * @param responseId
     * @return
     */
    public GetAiResultLatestResp getAiResultLatest(String responseId) {
        GetAiResultLatestReq getAiResultLatestReq = new GetAiResultLatestReq();
        getAiResultLatestReq.setResponseId(responseId);
        try {
            R<GetAiResultLatestResp> respR = apiServiceFeignClient.getAiResultLatest(getAiResultLatestReq);
            if (respR.isOk()) {
                GetAiResultLatestResp data = respR.getData();
                return data;
            }
        } catch (Exception e) {
            log.error("RPC查询最新识别结果异常!");
        }
        return null;
    }

    /**
     * 获取AI最后一次识别结果文件V2
     *
     * @param responseId
     * @return
     */
    public GetAiResultLatestResp getAiResultLatestV2(String responseId) {
        GetAiResultLatestReq getAiResultLatestReq = new GetAiResultLatestReq();
        getAiResultLatestReq.setResponseId(responseId);
        int retryCount = 0;
        int maxRetries = 5;
        while (retryCount < maxRetries) {
            try {
                R<GetAiResultLatestResp> respR = apiServiceFeignClient.getAiResultLatest(getAiResultLatestReq);
                if (respR.isOk()) {
                    GetAiResultLatestResp data = respR.getData();
                    if (data != null && (data.getQuestionList() != null && !data.getQuestionList().isEmpty())) {
                        return data;
                    }
                }
                // 如果questionList为空，等待500ms
                retryCount++;
                Thread.sleep(500);
            } catch (Exception e) {
                log.error("RPC查询最新识别结果异常!", e);
            }
        }
        return null;
    }

    /**
     * 查询答卷拼接图原图集合
     *
     * @param responseId
     * @param groupNo
     * @return
     */
    public GetStitchImageSourceImageResp getStitchImageSourceImageList(String responseId, Long groupNo) {
        GetResponseResultBaseReq req = new GetResponseResultBaseReq();
        GetResponseResultBaseReq.BaseData baseData = new GetResponseResultBaseReq.BaseData();
        baseData.setResponseId(responseId);
        baseData.setGroupNo(groupNo);
        req.setData(baseData);
        try {
            log.info("RPC查询拼接图片源图请求参数responseId={}, groupNo={}, req:{}", responseId, groupNo, JsonUtil.toJsonString(req));
            R<GetStitchImageSourceImageResp> respR = apiManagerFeignClient.getStitchImageSourceImageList(req);
            log.info("RPC查询拼接图片源图返回结果:{}", JsonUtil.toJsonString(respR));
            if (respR.isOk()) {
                GetStitchImageSourceImageResp data = respR.getData();
                return data;
            }
        } catch (Exception e) {
            log.error("RPC查询拼接图片源图异常!");
        }
        return null;
    }

}

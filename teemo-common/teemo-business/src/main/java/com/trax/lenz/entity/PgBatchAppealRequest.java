package com.trax.lenz.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 宝洁客户批量申诉请求记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PgBatchAppealRequest extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 批量申诉主键id
     */
    private String batchAppealId;

    /**
     * 组答卷id
     */
    private String responseGroupId;

    /**
     * 唯一请求id
     */
    private String customerRequestId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 类型 2.人工识别 3.申诉
     */
    private Integer type;

    /**
     * 答卷状态 1.待处理 2.申诉中 3.申诉完成 4.bi计算完成
     */
    private Integer status;

    private Date createTime;

    /**
     * 测试 0.否 1.是
     */
    private Integer test;

    /**
     * 申诉请求原始内容
     */
    private String reqContent;

    /**
     * 请求bi平台内容
     */
    private String biReqContent;

    /**
     * bi平台响应内容
     */
    private String biRespContent;

    /**
     * 回调状态 （1.成功 2.失败 3.暂停重试）
     */
    private Integer callbackStatus;

    /**
     * 累计回调次数
     */
    private Integer counts;

    /**
     * 是否需要重新推送（0.否 1.是）
     */
    private Integer retryEnabled;

    /**
     * 请求地址
     */
    private String callbackUrl;

    /**
     * 请求类型
     */
    private String callbackType;

    /**
     * 回调内容
     */
    private String callbackParam;

    /**
     * http响应码
     */
    private String callbackHttpCode;

    /**
     * http响应内容
     */
    private String callbackResponse;

    /**
     * 更新时间
     */
    private Date updateTime;


}

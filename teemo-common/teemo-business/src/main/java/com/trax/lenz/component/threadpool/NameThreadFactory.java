package com.trax.lenz.component.threadpool;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程池策略工厂
 */
public class NameThreadFactory implements ThreadFactory {

    private final AtomicInteger poolNumber = new AtomicInteger(1);
    private final AtomicInteger threadNumber = new AtomicInteger(1);
    private final ThreadGroup threadGroup;
    public final String namePrefix;

    NameThreadFactory(String name) {
        SecurityManager s = System.getSecurityManager();

        threadGroup = (s != null) ? s.getThreadGroup() : Thread.currentThread().getThreadGroup();

        if (null == name || "".equals(name.trim())) {
            name = "pool";
        }

        namePrefix = name + poolNumber.getAndIncrement() + "-thread-";
    }

    @Override
    public Thread newThread(Runnable r) {
        Thread t = new Thread(threadGroup, r, namePrefix + threadNumber.getAndIncrement(), 0);

        if (t.isDaemon()) {
            t.setDaemon(false);
        }

        if (t.getPriority() != Thread.NORM_PRIORITY) {
            t.setPriority(Thread.NORM_PRIORITY);
        }

        return t;
    }

}
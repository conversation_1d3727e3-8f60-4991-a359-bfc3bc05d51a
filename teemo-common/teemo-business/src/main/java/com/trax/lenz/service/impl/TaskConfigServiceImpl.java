package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trax.lenz.entity.TaskConfig;
import com.trax.lenz.enums.SubmitType;
import com.trax.lenz.mapper.TaskConfigMapper;
import com.trax.lenz.service.TaskConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 任务配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Service
public class TaskConfigServiceImpl extends ServiceImpl<TaskConfigMapper, TaskConfig> implements TaskConfigService {

    public TaskConfig getOne(String taskId, Integer code) {
        LambdaQueryWrapper<TaskConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskConfig::getTaskId, taskId);
        wrapper.eq(TaskConfig::getType, code);
        return getOne(wrapper);
    }
}

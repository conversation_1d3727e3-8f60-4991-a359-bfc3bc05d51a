package com.trax.lenz.config.prop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName FeiShuProperties
 * @Description:
 * <AUTHOR>
 * @Date 2023-02-02-10:34
 **/
@Data
@ConfigurationProperties(prefix = "feishu.notice")
@Component
public class FeiShuProperties {

    /**
     * 识别告警机器人
     */
    private String identifyRobotUrl;

    /**
     * 回调失败告警机器人
     */
    private String callbackFailRobotUrl;

    private String hadayRobotUrl;

    private String unileverSmallAppRobotUrl;
}

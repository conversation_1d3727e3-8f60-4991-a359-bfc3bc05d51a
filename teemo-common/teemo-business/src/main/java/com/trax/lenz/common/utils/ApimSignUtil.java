package com.trax.lenz.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ApimSignUtil
 * @Description: 项目加密工具类
 * <AUTHOR>
 * @Date 2024-05-14-14:11
 **/
@Slf4j
public class ApimSignUtil {

    private static final RestTemplate restTemplate = new RestTemplate();

    /**
     * 宝洁项目定制加密方法
     *
     * @param params
     * @param secret
     * @return
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     */
    public static String sign(Map<String, String> params, String secret) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        List<String> paramList = new ArrayList<>();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            paramList.add(entry.getKey() + "=" + entry.getValue());
        }
        Collections.sort(paramList);
        StringBuilder sb = new StringBuilder();
        sb.append(secret);
        for (String paramStr : paramList) {
            sb.append(paramStr);
            sb.append("&");
        }
        sb.replace(sb.length() - 1, sb.length(), secret);
        MessageDigest instance = MessageDigest.getInstance("SHA-256");
        byte[] bytes = instance.digest(sb.toString().getBytes("UTF-8"));
        return Hex.encodeHexString(bytes).toUpperCase();
    }

    /**
     * 宝洁项目定制文件上传方法
     *
     * @param paramMap
     * @param file
     * @param url
     * @param contentType
     * @return
     */
    public static String fileUploadRequest(Map<String, Object> paramMap, File file, String url, String contentType) {
        try {
            // 将文件内容转换为 ByteArrayResource
            byte[] fileBytes = Files.readAllBytes(file.toPath());
            ByteArrayResource fileAsResource = new ByteArrayResource(fileBytes) {
                @Override
                public String getFilename() {
                    return file.getName();
                }
            };

            HttpHeaders headers = new HttpHeaders();

            HttpHeaders fileHeaders = new HttpHeaders();
            fileHeaders.setContentType(MediaType.parseMediaType(contentType));
            fileHeaders.setContentDispositionFormData("file", file.getName());

            // 创建 HttpEntity 用于上传文件部分
            HttpEntity<ByteArrayResource> fileEntity = new HttpEntity<>(fileAsResource, fileHeaders);

            MultiValueMap<String, Object> hashMap = new LinkedMultiValueMap<>();
            for (String key : paramMap.keySet()) {
                hashMap.add(key, paramMap.get(key));
            }
            hashMap.add("file", fileEntity);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(hashMap, headers);

            // 发起 POST 请求
            String response = restTemplate.postForObject(url, requestEntity, String.class);
            log.info("宝洁文件上传成功, resp={}", response);
            return response;
        } catch (IOException e) {
            log.error("宝洁文件上传失败:", e);
            throw new RuntimeException("宝洁文件上传失败");
        }
    }

    /**
     * 生成宝洁签名
     *
     * @param paramsMap
     * @return
     */
    public static String signature(Map<String, String> paramsMap) {
        try {
            List<String> list = new ArrayList<>();
            for (Map.Entry<String, String> element : paramsMap.entrySet()) {
                list.add(concatParamStr(element.getKey(), element.getValue()));
            }
            StringBuilder sb = new StringBuilder();
            Collections.sort(list);
            for (String sigParam : list) {
                sb.append(sigParam);
            }
            return DigestUtils.sha1Hex(sb.toString().getBytes(StandardCharsets.UTF_8.name()));
        } catch (Exception e) {
            log.error("生成宝洁签名失败:", e);
        }
        return "";
    }

    public static String concatParamStr(String key, String value) {
        return key.concat("=").concat(value);
    }

    /**
     * 获取PEC授权信息
     *
     * @param username
     * @param password
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String getPecAuthorization(String username, String password) throws UnsupportedEncodingException {
        return Base64.getEncoder().encodeToString((username + ":" + password).getBytes("UTF-8"));
    }

    /**
     * PEC RSA鉴权加密实现
     *
     * @param data
     * @param publicKeyStr
     * @return
     * @throws Exception
     */
    public static String encrypt(String data, String publicKeyStr) throws Exception {
        // 将公钥字符串转换为 PublicKey 对象
        byte[] keyBytes = Base64.getDecoder().decode(publicKeyStr);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(spec);
        // 使用 RSA 加密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] encryptedBytes = cipher.doFinal(data.getBytes());
        // 返回 Base64 编码的加密结果
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

}

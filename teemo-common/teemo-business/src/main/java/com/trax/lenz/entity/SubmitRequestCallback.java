package com.trax.lenz.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 识别回调表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SubmitRequestCallback extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 请求id
     */
    private String id;

    /**
     * 项目code（参见paladin）
     */
    private String projectCode;

    /**
     * 唯一请求id
     */
    private String customerRequestId;

    /**
     * 答卷id
     */
    private String responseId;

    /**
     * 流程编号（首次识别为1，重跑累加）
     */
    private Integer processNo;

    /**
     * 当前最新状态（1.初始化 2.查询成功 3.查询失败 4.回调成功 5.回调失败）
     */
    private Integer status;

    /**
     * 累计回调次数
     */
    private Integer counts;

    /**
     * 是否需要重新推送（0.否 1.是）
     */
    private Integer retryEnabled;

    /**
     * 请求地址
     */
    private String callbackUrl;

    /**
     * 请求类型
     */
    private String callbackType;

    /**
     * 回调内容
     */
    private String callbackParam;

    /**
     * http响应码
     */
    private String callbackHttpCode;

    /**
     * http响应内容
     */
    private String callbackResponse;


}

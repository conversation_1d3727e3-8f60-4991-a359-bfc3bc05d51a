package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trax.lenz.entity.SubmitRequestIdMapping;
import com.trax.lenz.mapper.SubmitRequestIdMappingMapper;
import com.trax.lenz.service.SubmitRequestIdMappingService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 客户识别请求记录id关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Service
public class SubmitRequestIdMappingServiceImpl extends ServiceImpl<SubmitRequestIdMappingMapper, SubmitRequestIdMapping> implements SubmitRequestIdMappingService {

}

package com.trax.lenz.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 统一查重请求记录明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PecRepeatRequestDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String detailId;

    /**
     * trax唯一Id
     */
    private String checkRepeatId;

    /**
     * 组织Id
     */
    private String branchId;

    /**
     * 统一拜访人员Id
     */
    private String salesId;

    /**
     * 状态 1.待处理 2.处理中 3.处理完成
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 测试 0.否 1.是
     */
    private Integer test;

    /**
     * 客户查重原图
     */
    private String imageOriginJson;

    /**
     * 客户查重对比图
     */
    private String imageCompareJson;

    /**
     * 翻拍请求Id
     */
    private String responseId;

    /**
     * 翻拍状态 1.待处理 2.处理中 3.处理完成
     */
    private Integer remakeStatus;

    /**
     * 查重Id
     */
    private String repeatId;

    /**
     * 查重状态 1.待处理 2.处理中 3.处理完成
     */
    private Integer repeatStatus;

    /**
     * 回调状态 （1.成功 2.失败 3.暂停重试）
     */
    private Integer callbackStatus;

    /**
     * 累计回调次数
     */
    private Integer counts;

    /**
     * 是否需要重新推送（0.否 1.是）
     */
    private Integer retryEnabled;

    /**
     * 请求地址
     */
    private String callbackUrl;

    /**
     * 请求类型
     */
    private String callbackType;

    /**
     * 回调内容
     */
    private String callbackParam;

    /**
     * http响应码
     */
    private String callbackHttpCode;

    /**
     * http响应内容
     */
    private String callbackResponse;

    /**
     * 更新时间
     */
    private Date updateTime;


}

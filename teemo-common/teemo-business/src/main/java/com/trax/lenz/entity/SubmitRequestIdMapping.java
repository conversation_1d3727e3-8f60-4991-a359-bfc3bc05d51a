package com.trax.lenz.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 客户识别请求记录id关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SubmitRequestIdMapping extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 答卷id
     */
    private String responseId;

    /**
     * 项目code（参见paladin）
     */
    private String projectCode;

    /**
     * 唯一请求id
     */
    private String customerRequestId;


}

package com.trax.lenz.service.remote;

import com.trax.lenz.common.utils.DateUtil;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.dto.request.submit.PgIdentifySubmitReq;
import com.trax.lenz.dto.response.PgIdentifySubmitResp;
import com.trax.lenz.message.protobuf.brain_servicepb.ComprehensiveGrpc;
import com.trax.lenz.message.protobuf.brain_servicepb.ComprehensiveOuterClass;
import com.trax.lenz.message.protobuf.brain_servicepb.ComprehensiveOuterClass.ComprehensiveRequest;
import com.trax.lenz.message.protobuf.brain_servicepb.ComprehensiveOuterClass.ComprehensiveResponse;
import com.trax.lenz.message.protobuf.brain_servicepb.Utils;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.Metadata;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.MetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @ClassName GrpcClientService
 * @Description: GRPC定制识别服务实现类
 * <AUTHOR>
 * @Date 2024-07-25-15:30
 **/
@Slf4j
@Service
public class GrpcClientService {

    public PgIdentifySubmitResp pgSubmit(String pgIdentifySubmitReq) {
        PgIdentifySubmitReq req = JsonUtil.jsonToPojo(pgIdentifySubmitReq, PgIdentifySubmitReq.class);
        List<Utils.ImageSource> requestList = Lists.newArrayList();
        for (PgIdentifySubmitReq.Image image : req.getImageList()) {
            Utils.ImageSource comprehensiveRequest = Utils.ImageSource.newBuilder().setUrl(image.getImageUrl()).setId(image.getImageId()).build();
            requestList.add(comprehensiveRequest);
        }
        // 创建通道
        ManagedChannel channel = ManagedChannelBuilder.forAddress(req.getAddress(), 443).useTransportSecurity().build();
        try {
            // 创建Stub
            ComprehensiveGrpc.ComprehensiveBlockingStub stub = ComprehensiveGrpc.newBlockingStub(channel);
            // 创建请求对象
            ComprehensiveRequest.Builder requestBuilder = ComprehensiveRequest.newBuilder().setThresh(0.9f).setEnableRemake(req.getEnableRemake()).setEnableVague(req.getEnableVague()).setEnableAngle(req.getEnableAngle()).addAllImages(requestList);
            ComprehensiveRequest request = requestBuilder.build();
            // 设置元数据
            Metadata metadata = new Metadata();
            metadata.put(Metadata.Key.of("from_caller", Metadata.ASCII_STRING_MARSHALLER), req.getFromCaller());
            metadata.put(Metadata.Key.of("from_business", Metadata.ASCII_STRING_MARSHALLER), req.getFromBusiness());
            metadata.put(Metadata.Key.of("request_id", Metadata.ASCII_STRING_MARSHALLER), req.getResponseId());
            metadata.put(Metadata.Key.of("async_recv", Metadata.ASCII_STRING_MARSHALLER), "0");
            metadata.put(Metadata.Key.of("request_time", Metadata.ASCII_STRING_MARSHALLER), DateUtil.getFormatDate(new Date(), DateUtil.DATE_FORMAT_3));
            // 将元数据添加到stub
            stub = MetadataUtils.attachHeaders(stub, metadata);
            ComprehensiveResponse response = stub.checkComprehensive(request);
            PgIdentifySubmitResp resp = new PgIdentifySubmitResp();
            resp.setResponseId(req.getResponseId());
            List<PgIdentifySubmitResp.Result> resultList = Lists.newArrayList();
            for (ComprehensiveOuterClass.ComprehensiveResult comprehensiveResult : response.getResultsList()) {
                PgIdentifySubmitResp.Result result = new PgIdentifySubmitResp.Result();
                double number = Double.parseDouble(String.valueOf(comprehensiveResult.getRemakeScore()));
                // 创建 DecimalFormat 实例来格式化数字
                DecimalFormat df = new DecimalFormat("0.#####");
                // 格式化数字为正常的字符串
                String normalNumber = df.format(number);
                result.setImageId(comprehensiveResult.getId());
                result.setRemakeScore(normalNumber);
                result.setVagueScore(String.valueOf(comprehensiveResult.getVagueScore()));
                result.setAngle(String.valueOf(comprehensiveResult.getAngle()));
                resultList.add(result);
            }
            resp.setResultList(resultList);
            return resp;
        } catch (StatusRuntimeException e) {
            e.printStackTrace();
        } finally {
            channel.shutdown();
        }
        return null;
    }

    public static void main(String[] args) {
        List<Utils.ImageSource> requestList = Lists.newArrayList();
        requestList.add(Utils.ImageSource.newBuilder().setUrl("https://trax-ai-cos.langjtech.com/rule0/1242342929867997184/1951768963885105152/1557532507035992064/20240725/2780628554579247104/hermes/1557567141652267008/2780628588938985473.jpg").setId("123").build());
        requestList.add(Utils.ImageSource.newBuilder().setUrl("https://trax-ai-cos.langjtech.com/rule0/1388767951355641856/1388776266949263360/6c26cdc6c62b47ea825589805f95ed34/20240725/2780605086877941760/hermes/1412271043728441344/2780605086877941762.png").setId("456").build());
        requestList.add(Utils.ImageSource.newBuilder().setUrl("https://trax-ai-cos.langjtech.com/rule0/2613949463219142656/2613950906865090560/2613952831010439168/20240725/2780537088955711488/hermes/2779925210734854144/2780537088955711491.jpg").setId("789").build());
        // 创建通道
        ManagedChannel channel = ManagedChannelBuilder.forAddress("apisix-gateway-uat.langjtech.com", 443).useTransportSecurity().build();
        try {
            // 创建Stub
            ComprehensiveGrpc.ComprehensiveBlockingStub stub = ComprehensiveGrpc.newBlockingStub(channel);
            // 创建请求对象
            ComprehensiveRequest.Builder requestBuilder = ComprehensiveRequest.newBuilder().setThresh(0.9f).setEnableRemake(true).setEnableVague(true).setEnableAngle(true).addAllImages(requestList);
            ComprehensiveRequest request = requestBuilder.build();
            // 生成唯一请求ID
            String requestId = UUID.randomUUID().toString();
            // 设置元数据
            Metadata metadata = new Metadata();
            metadata.put(Metadata.Key.of("from_caller", Metadata.ASCII_STRING_MARSHALLER), "TEST_PG");
            metadata.put(Metadata.Key.of("from_business", Metadata.ASCII_STRING_MARSHALLER), "TEST_PG");
            metadata.put(Metadata.Key.of("request_id", Metadata.ASCII_STRING_MARSHALLER), requestId);
            metadata.put(Metadata.Key.of("async_recv", Metadata.ASCII_STRING_MARSHALLER), "0");
            metadata.put(Metadata.Key.of("request_time", Metadata.ASCII_STRING_MARSHALLER), DateUtil.getFormatDate(new Date(), DateUtil.DATE_FORMAT_3));
            // 将元数据添加到stub
            stub = MetadataUtils.attachHeaders(stub, metadata);
            // 发送请求并接收响应
            ComprehensiveResponse response = stub.checkComprehensive(request);
            for (ComprehensiveOuterClass.ComprehensiveResult comprehensiveResult : response.getResultsList()) {
                log.info("comprehensiveResult: {}", comprehensiveResult);
            }
        } catch (StatusRuntimeException e) {
            e.printStackTrace();
        } finally {
            channel.shutdown();
        }
    }

}

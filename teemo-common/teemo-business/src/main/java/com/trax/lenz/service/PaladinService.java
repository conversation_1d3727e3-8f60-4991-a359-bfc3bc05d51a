package com.trax.lenz.service;

import com.trax.lenz.common.constants.AppConstants;
import com.trax.lenz.common.core.domain.R;
import com.trax.lenz.common.utils.HtmlUtil;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.paladin.api.remote.AiTaskFeignClient;
import com.trax.lenz.paladin.api.remote.PaladinManagerFeignClient;
import com.trax.lenz.paladin.api.remote.PaladinServiceFeignClient;
import com.trax.lenz.paladin.api.remote.TreeFeignClient;
import com.trax.lenz.paladin.domain.http.RestResult;
import com.trax.lenz.paladin.domain.old.product.RecognizeQuestionRowVO;
import com.trax.lenz.paladin.domain.old.tree.ImageProductTreeDetailVO;
import com.trax.lenz.paladin.domain.response.AiTaskConfigResp;
import com.trax.lenz.paladin.domain.response.GetTaskConfigForTeemoResp;
import com.trax.lenz.paladin.domain.response.GetTaskDtoResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调用paladin服务接口Service
 *
 * <AUTHOR>
 * @date 2022-01-18 11:13:40
 */
@Slf4j
@Service
public class PaladinService {

    private volatile Map<String, AiTaskConfigResp> configRespMap = new HashMap<>();

    private volatile Map<Long, String> skuMap = new HashMap<>();

    private volatile Map<String, GetTaskDtoResp> taskMap = new HashMap<>();

    private volatile Map<String, Integer> biCompanyIdMap = new HashMap<>();

    private volatile Map<String, List<RecognizeQuestionRowVO>> recognizeQuestionRowMap = new HashMap<>();

    private final AiTaskFeignClient aiTaskFeignClient;

    private final TreeFeignClient treeFeignClient;

    private final PaladinServiceFeignClient paladinServiceFeignClient;

    private final PaladinManagerFeignClient paladinManagerFeignClient;

    public PaladinService(AiTaskFeignClient aiTaskFeignClient, TreeFeignClient treeFeignClient, PaladinServiceFeignClient paladinServiceFeignClient, PaladinManagerFeignClient paladinManagerFeignClient) {
        this.aiTaskFeignClient = aiTaskFeignClient;
        this.treeFeignClient = treeFeignClient;
        this.paladinServiceFeignClient = paladinServiceFeignClient;
        this.paladinManagerFeignClient = paladinManagerFeignClient;
    }

    /**
     * 清空内存（10分钟）
     *
     */
    @Scheduled(fixedRate = 10 * 60 * 1000)
    public void clearMap(){
        log.info("每10分钟清空paladin本地缓存!");
        configRespMap.clear();
        skuMap.clear();
        taskMap.clear();
        biCompanyIdMap.clear();
        recognizeQuestionRowMap.clear();
    }

    /**
     * 查询题目配置信息
     *
     * 增加缓存，缓存有效期60S
     *
     * @param questionId
     * @return AiTaskConfigResp
     */
    public AiTaskConfigResp getQuestionConfig(String questionId) {
        // 从内存中获取
        if (configRespMap.containsKey(questionId)){
            AiTaskConfigResp aiTaskConfigResp = configRespMap.get(questionId);
            log.info("从内存中获取到题目配置！questionId={}, aiTaskConfigResp={}", questionId, JsonUtil.toJsonString(aiTaskConfigResp));
            return aiTaskConfigResp;
        }

        try {
            RestResult<AiTaskConfigResp> aiTaskConfigRespRestResult = aiTaskFeignClient.getAiTaskConfig(questionId);
            log.info("rpc-paladin服务-查询题目配置信息！questionId={}, returnJson={}", questionId, JsonUtil.toJsonString(aiTaskConfigRespRestResult));
            if (aiTaskConfigRespRestResult.getCode() == 0) {
                AiTaskConfigResp aiTaskConfigResp = aiTaskConfigRespRestResult.getData();
                aiTaskConfigResp.setQuestionName(HtmlUtil.delHTMLTag(aiTaskConfigResp.getQuestionName()));
                configRespMap.put(questionId, aiTaskConfigResp);
                return aiTaskConfigResp;
            }
        } catch (Exception e) {
            log.error("rpc-paladin服务-查询题目配置信息接口异常！questionId={}", questionId, e);
        }
        return null;
    }

    /**
     * 是否为拼接题
     *
     * @param questionId
     * @return boolean
     */
    public boolean isStitchQuestion(String questionId) {
        AiTaskConfigResp aiTaskConfigResp = getQuestionConfig(questionId);
        if (aiTaskConfigResp != null && CollectionUtils.isNotEmpty(aiTaskConfigResp.getFeatureEnNameList())) {
            for (String featureEnName : aiTaskConfigResp.getFeatureEnNameList()) {
                if (StringUtils.contains(featureEnName, AppConstants.STITCH)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 查询任务id
     *
     * @param questionId
     * @return boolean
     */
    public String getTaskId(String questionId) {
        AiTaskConfigResp aiTaskConfigResp = getQuestionConfig(questionId);
        return aiTaskConfigResp.getTask().getTaskId();
    }

    /**
     * 查询商品名称
     *
     * 增加缓存，缓存有效期60S
     *
     * @param skuId
     * @return String
     */
    public String getSkuName(Long skuId) {
        if (skuMap.containsKey(skuId)){
            return skuMap.get(skuId);
        }
        try {
            RestResult<ImageProductTreeDetailVO> imageProductTreeDetailVORestResult = (RestResult<ImageProductTreeDetailVO>) treeFeignClient.detail(skuId);
            log.info("rpc-paladin服务-查询sku信息！skuId={}, returnJson={}", skuId, JsonUtil.toJsonString(imageProductTreeDetailVORestResult));
            if (imageProductTreeDetailVORestResult.getCode() == 0) {
                ImageProductTreeDetailVO vo = imageProductTreeDetailVORestResult.getData();
                skuMap.put(skuId, vo.getName());
                return vo.getName();
            }
        } catch (Exception e) {
            log.error("rpc-paladin服务-查询sku信息异常！skuId={}", skuId, e);
        }
        return null;
    }

    /**
     * 查询任务信息
     *
     * @param taskId
     * @return String
     */
    public GetTaskDtoResp getTaskObj(String taskId) {
        if (taskMap.containsKey(taskId)){
            return taskMap.get(taskId);
        }
        try {
            RestResult<GetTaskDtoResp> result = aiTaskFeignClient.getTaskObjForTaskId(taskId);
            log.info("rpc-paladin服务-查询任务信息！taskId={}, returnJson={}", taskId, JsonUtil.toJsonString(result));
            if (result != null && result.getCode() == 0 && result.getData() != null) {
                GetTaskDtoResp resp = result.getData();
                taskMap.put(taskId, resp);
                return resp;
            }
        } catch (Exception e) {
            log.error("rpc-paladin服务-查询任务信息异常！taskId={}", taskId, e);
        }
        return null;
    }

    /**
     * 获取任务回调配置（paladin已做缓存，调用方无需处理）
     *
     * @param taskId
     * @return
     */
    public GetTaskConfigForTeemoResp getTaskTeemoConfig(String taskId) {
        try {
            log.info("RPC查询teemo任务配置请求taskId:{}", taskId);
            RestResult<GetTaskConfigForTeemoResp> taskCallbackConfig = aiTaskFeignClient.getProjectTaskTeemoConfig(taskId);
            log.info("RPC查询teemo任务配置返回结果:{}", JsonUtil.toJsonString(taskCallbackConfig));
            if (0 == taskCallbackConfig.getCode()) {
                return taskCallbackConfig.getData();
            }
        } catch (Exception e) {
            log.error("RPC查询teemo任务配置异常！taskId:{}", taskId);
        }
        return null;
    }

    /**
     * 获取任务回调配置（paladin已做缓存，调用方无需处理）
     * @param taskId
     * @return
     */
    public Integer selectBiCompanyIdByTaskId(String taskId) {
        if (biCompanyIdMap.containsKey(taskId)) {
            return biCompanyIdMap.get(taskId);
        }
        try {
            log.info("RPC查询任务对应的biCompanyId请求taskId:{}", taskId);
            R<Integer> integerR = paladinServiceFeignClient.selectBICompanyIdByTaskId(taskId);
            log.info("RPC查询任务对应的biCompanyId返回结果:{}", JsonUtil.toJsonString(integerR));
            if (integerR != null && integerR.isOk()) {
                biCompanyIdMap.put(taskId, integerR.getData());
                return integerR.getData();
            }
        } catch (Exception e) {
            log.error("RPC查询任务对应的biCompanyId异常！taskId:{}", taskId);
        }
        return null;
    }

    /**
     * 获取当前任务识别题目对应步骤
     *
     * @param taskId
     * @return
     */
    public List<RecognizeQuestionRowVO> getRecognizeQuestionRow(String taskId) {
        if (recognizeQuestionRowMap.containsKey(taskId)) {
            return recognizeQuestionRowMap.get(taskId);
        }
        try {
            log.info("RPC查询任务识别题目对应步骤请求taskId:{}", taskId);
            RestResult<RecognizeQuestionRowVO> resp = paladinManagerFeignClient.getList(taskId);
            log.info("RPC查询任务识别题目对应步骤返回结果:{}", JsonUtil.toJsonString(resp));
            if (resp.getCode() == 0) {
                recognizeQuestionRowMap.put(taskId, resp.getDatas());
                return resp.getDatas();
            }
        } catch (Exception e) {
            log.error("RPC查询任务对应的biCompanyId异常！taskId:{}", taskId, e);
        }
        return null;
    }
}

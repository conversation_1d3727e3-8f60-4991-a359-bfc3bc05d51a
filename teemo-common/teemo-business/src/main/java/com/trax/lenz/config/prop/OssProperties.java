package com.trax.lenz.config.prop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 *
 * OssProperties
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "cloud.storage.oss")
@Component
public class OssProperties {

  private String accessKeyId;

  private String accessKeySecret;

  private String bucketName;

  private String domain;

  private String endpoint;

}

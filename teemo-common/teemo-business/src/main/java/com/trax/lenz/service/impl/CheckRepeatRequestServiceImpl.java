package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trax.lenz.entity.CheckRepeatRequest;
import com.trax.lenz.mapper.CheckRepeatRequestMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 客户查重请求记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Service
public class CheckRepeatRequestServiceImpl extends ServiceImpl<CheckRepeatRequestMapper, CheckRepeatRequest> {

    public CheckRepeatRequest getOne(String checkRepeatId) {
        LambdaQueryWrapper<CheckRepeatRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CheckRepeatRequest::getCheckRepeatId, checkRepeatId);
        return getOne(wrapper);
    }

    public CheckRepeatRequest getOneByResponseId(String responseId) {
        LambdaQueryWrapper<CheckRepeatRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CheckRepeatRequest::getResponseId, responseId);
        return getOne(wrapper);
    }

    public void update(String checkRepeatId, CheckRepeatRequest appealRequest) {
        LambdaQueryWrapper<CheckRepeatRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CheckRepeatRequest::getCheckRepeatId, checkRepeatId);
        update(appealRequest, wrapper);
    }

    public boolean remove(String checkRepeatId) {
        LambdaQueryWrapper<CheckRepeatRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CheckRepeatRequest::getCheckRepeatId, checkRepeatId);
        return remove(wrapper);
    }

    public boolean isAllFinish(String checkRepeatId) {
        LambdaQueryWrapper<CheckRepeatRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CheckRepeatRequest::getCustomerRequestId, checkRepeatId);
        wrapper.ne(CheckRepeatRequest::getStatus, 3);
        return count(wrapper) == 0;
    }

    public List<CheckRepeatRequest> getSonList(String checkRepeatId) {
        LambdaQueryWrapper<CheckRepeatRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CheckRepeatRequest::getCustomerRequestId, checkRepeatId);
        return list(wrapper);
    }

    public CheckRepeatRequest getOneByCustomerRequestId(String customerRequestId) {
        LambdaQueryWrapper<CheckRepeatRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CheckRepeatRequest::getCustomerRequestId, customerRequestId);
        return getOne(wrapper);
    }
}

package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trax.lenz.entity.AppealRequest;
import com.trax.lenz.entity.PgBatchAppealRequest;
import com.trax.lenz.mapper.PgBatchAppealRequestMapper;
import com.trax.lenz.service.IPgBatchAppealRequestService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 宝洁客户批量申诉请求记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-13
 */
@Service
public class PgBatchAppealRequestServiceImpl extends ServiceImpl<PgBatchAppealRequestMapper, PgBatchAppealRequest> implements IPgBatchAppealRequestService {

    public PgBatchAppealRequest getOne(String appealId) {
        LambdaQueryWrapper<PgBatchAppealRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PgBatchAppealRequest::getBatchAppealId, appealId);
        return getOne(wrapper);
    }

    public void update(String batchAppealId, PgBatchAppealRequest batchAppealRequest) {
        LambdaQueryWrapper<PgBatchAppealRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PgBatchAppealRequest::getBatchAppealId, batchAppealId);
        update(batchAppealRequest, wrapper);
    }
}

package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trax.lenz.entity.IdentifyRequest;
import com.trax.lenz.mapper.IdentifyRequestMapper;
import com.trax.lenz.service.IdentifyRequestService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 客户识别请求记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Service
public class IdentifyRequestServiceImpl extends ServiceImpl<IdentifyRequestMapper, IdentifyRequest> implements IdentifyRequestService {

    public IdentifyRequest getOne(String responseId) {
        LambdaQueryWrapper<IdentifyRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IdentifyRequest::getResponseId, responseId);
        return getOne(wrapper);
    }

    public void update(String responseId, IdentifyRequest identifyRequest) {
        LambdaQueryWrapper<IdentifyRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IdentifyRequest::getResponseId, responseId);
        update(identifyRequest, wrapper);
    }

    public boolean remove(String responseId) {
        LambdaQueryWrapper<IdentifyRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IdentifyRequest::getResponseId, responseId);
        return remove(wrapper);
    }

}

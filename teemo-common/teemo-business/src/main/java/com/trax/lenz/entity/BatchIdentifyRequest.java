package com.trax.lenz.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 客户批量计算请求记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BatchIdentifyRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 答卷组Id
     */
    @TableId
    private String responseGroupId;

    /**
     * 客户唯一请求组id
     */
    private String customerGroupId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 答卷状态 1.待处理 2.识别中 3.识别完成
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 测试 0.否 1.是
     */
    private Integer test;

    /**
     * 客户请求原始内容
     */
    private String reqContent;

    /**
     * 请求bi平台内容
     */
    private String biReqContent;

    /**
     * 识别完成时间
     */
    private Date identifyFinishTime;

    /**
     * bi平台响应内容
     */
    private String biRespContent;

    /**
     * 回调状态 （1.成功 2.失败 3.暂停重试）
     */
    private Integer callbackStatus;

    /**
     * 累计回调次数
     */
    private Integer counts;

    /**
     * 是否需要重新推送（0.否 1.是）
     */
    private Integer retryEnabled;

    /**
     * 请求地址
     */
    private String callbackUrl;

    /**
     * 请求类型
     */
    private String callbackType;

    /**
     * 回调内容
     */
    private String callbackParam;

    /**
     * http响应码
     */
    private String callbackHttpCode;

    /**
     * http响应内容
     */
    private String callbackResponse;

    /**
     * 更新时间
     */
    private Date updateTime;


}

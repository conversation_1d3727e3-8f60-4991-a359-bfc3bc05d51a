package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trax.lenz.entity.PgBatchAppealRequest;
import com.trax.lenz.entity.PgBatchAppealRequestNonRealtime;
import com.trax.lenz.mapper.PgBatchAppealRequestNonRealtimeMapper;
import com.trax.lenz.service.IPgBatchAppealRequestNonRealtimeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 宝洁客户批量申诉请求记录表(非实时) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-11-06
 */
@Service
public class PgBatchAppealRequestNonRealtimeServiceImpl extends ServiceImpl<PgBatchAppealRequestNonRealtimeMapper, PgBatchAppealRequestNonRealtime> implements IPgBatchAppealRequestNonRealtimeService {
    public PgBatchAppealRequestNonRealtime getOne(String appealId) {
        LambdaQueryWrapper<PgBatchAppealRequestNonRealtime> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PgBatchAppealRequestNonRealtime::getBatchAppealId, appealId);
        return getOne(wrapper);
    }

    public void update(String batchAppealId, PgBatchAppealRequestNonRealtime batchAppealRequest) {
        LambdaQueryWrapper<PgBatchAppealRequestNonRealtime> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PgBatchAppealRequestNonRealtime::getBatchAppealId, batchAppealId);
        update(batchAppealRequest, wrapper);
    }
}

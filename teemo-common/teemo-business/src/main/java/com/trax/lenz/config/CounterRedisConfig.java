package com.trax.lenz.config;

import com.trax.lenz.config.prop.CounterRedisProperties;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

/**
 * 计数器 Redis 配置
 *
 * <AUTHOR>
 * @date 2024-04-28
 */
@Configuration
public class CounterRedisConfig {

    private final CounterRedisProperties counterRedisProperties;

    public CounterRedisConfig(CounterRedisProperties counterRedisProperties) {
        this.counterRedisProperties = counterRedisProperties;
    }

    @Bean(name = "counterRedisTemplate")
    public RedisTemplate<String, Integer> counterRedisTemplate() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(counterRedisProperties.getHost());
        config.setPort(counterRedisProperties.getPort());
        config.setDatabase(counterRedisProperties.getDatabase());
        config.setPassword(counterRedisProperties.getPassword());
        
        // 配置连接池
        GenericObjectPoolConfig<?> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxTotal(counterRedisProperties.getPool().getMaxActive());
        poolConfig.setMaxWaitMillis(counterRedisProperties.getPool().getMaxWait() * 1000L);
        poolConfig.setMaxIdle(counterRedisProperties.getPool().getMaxIdle());
        poolConfig.setMinIdle(counterRedisProperties.getPool().getMinIdle());
        
        // 创建 Lettuce 客户端配置
        LettuceClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
                .commandTimeout(Duration.ofSeconds(5))
                .shutdownTimeout(Duration.ofSeconds(2))
                .poolConfig(poolConfig)
                .build();
        
        // 创建连接工厂
        LettuceConnectionFactory lettuceConnectionFactory = new LettuceConnectionFactory(config, clientConfig);
        lettuceConnectionFactory.afterPropertiesSet();
        
        // 创建 RedisTemplate
        RedisTemplate<String, Integer> template = new RedisTemplate<>();
        template.setConnectionFactory(lettuceConnectionFactory);
        // 设置序列化器等其他配置
        template.setKeySerializer(keySerializer());
        template.setHashKeySerializer(keySerializer());
        template.setValueSerializer(valueSerializer());
        template.setHashValueSerializer(valueSerializer());
        return template;
    }

    private RedisSerializer<String> keySerializer() {
        return new StringRedisSerializer();
    }

    /**
     * 使用Jackson序列化器
     * @return
     */
    private RedisSerializer<Object> valueSerializer() {
        return new GenericJackson2JsonRedisSerializer();
    }
} 
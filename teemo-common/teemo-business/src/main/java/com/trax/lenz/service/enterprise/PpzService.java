package com.trax.lenz.service.enterprise;

import com.trax.lenz.config.prop.CustomConfigProperties;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName PpzService
 * @Description: ppz项目定制实现
 * <AUTHOR>
 * @Date 2024-05-08-18:17
 **/
@Service
@Slf4j
public class PpzService implements BaseService {

    private final CustomConfigProperties customConfigProperties;

    public PpzService(CustomConfigProperties customConfigProperties) {
        this.customConfigProperties = customConfigProperties;
    }

    @Override
    public boolean isSupport(String customerId) {
        return customConfigProperties.getNewPpz().getCustomerId().equals(customerId);
    }

    @Override
    public String getCallbackMethod(String responseId, Object result) {
        return customConfigProperties.getNewPpz().getCallbackMethod();
    }
}

package com.trax.lenz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trax.lenz.entity.SubmitRequestCallback;
import com.trax.lenz.mapper.SubmitRequestCallbackMapper;
import com.trax.lenz.service.SubmitRequestCallbackService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 识别回调表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@Service
public class SubmitRequestCallbackServiceImpl extends ServiceImpl<SubmitRequestCallbackMapper, SubmitRequestCallback> implements SubmitRequestCallbackService {

    /**
     * 查询回调次数
     *
     * @param responseId
     * @return
     */
    public Integer getProcessNoByResponseId(String responseId) {
        LambdaQueryWrapper<SubmitRequestCallback> callbackWrapper = new LambdaQueryWrapper<>();
        callbackWrapper.eq(SubmitRequestCallback::getResponseId, responseId);
        List<SubmitRequestCallback> requestCallbackList = list(callbackWrapper);
        if (Objects.isNull(requestCallbackList)) {
            return 1;
        }
        return requestCallbackList.size() + 1;
    }
}

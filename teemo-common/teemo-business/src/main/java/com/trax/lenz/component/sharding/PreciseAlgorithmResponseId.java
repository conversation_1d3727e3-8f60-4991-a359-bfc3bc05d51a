package com.trax.lenz.component.sharding;

import com.trax.lenz.common.core.id.SnowFlakeFactory;
import com.trax.lenz.common.core.util.SpringUtils;
import com.trax.lenz.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import java.text.SimpleDateFormat;
import java.util.Collection;

/**
 * responseId标准分片规则
 *
 * @author: yang.yu
 * @date: 2022-04-11 16:32
 */
@Slf4j
public class PreciseAlgorithmResponseId implements PreciseShardingAlgorithm<String> {

    private SnowFlakeFactory snowFlakeFactory;

    public void init() {
        if (snowFlakeFactory == null) {
            snowFlakeFactory = SpringUtils.getBean(SnowFlakeFactory.class);
        }
    }

    /**
     * 分片规则
     * <p>
     * 从responseId中提取时间，如果提取后的时间是时间格式同时大于202205，则返回月份分片，否则返回other
     *
     * @param tables
     * @param preciseShardingValue
     * @return String
     */
    @Override
    public String doSharding(Collection<String> tables, PreciseShardingValue<String> preciseShardingValue) {
        init();
        String baseTableName = preciseShardingValue.getLogicTableName();
        String shardingTableName = baseTableName;
        // 从responseId中提取时间
        String month = snowFlakeFactory.getDateMoth(preciseShardingValue.getValue());
        if (StringUtils.isNotBlank(month) && isMonth(month)) {
            shardingTableName = baseTableName + "_" + month;
        }
        log.info("shardingTableName={}", shardingTableName);
        return shardingTableName;
    }

    public static void main(String[] args) {
        System.out.println();
        System.out.println(isMonth("419813"));
    }

    private static boolean isMonth(String month) {
        try {
            SimpleDateFormat simple = new SimpleDateFormat(DateUtil.DATE_FORMAT_21);
            simple.setLenient(false);
            simple.parse(month);
        } catch (Exception e) {
            return false;
        }
        return true;
    }
}

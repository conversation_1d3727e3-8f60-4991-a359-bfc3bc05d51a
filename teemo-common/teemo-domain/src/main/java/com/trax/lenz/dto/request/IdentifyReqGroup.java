package com.trax.lenz.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 识别请求-图片组
 *
 * <AUTHOR>
 * @date 2022-03-28 11:13:22
 */
@Data
public class IdentifyReqGroup {

    @ApiModelProperty(value = "题目id", required = true)
    private String questionId;

    @ApiModelProperty(value = "组id", required = true)
    private String groupId;

    private String customerImageGroupNo;

    /**
     * 拼接方向
     */
    private Integer stitchOrientation;

    @ApiModelProperty(value = "图片集合", required = true)
    private List<IdentifyReqImage> imageList;

}

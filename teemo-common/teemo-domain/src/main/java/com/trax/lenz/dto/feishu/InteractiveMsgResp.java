package com.trax.lenz.dto.feishu;

import lombok.Data;

import java.util.List;

/**
 * @ClassName 飞书卡片
 * @Description:
 * <AUTHOR>
 * @Date 2023-01-09-13:58
 **/
@Data
public class InteractiveMsgResp {

    private String msg_type;

    private Card card;
    @Data
    public static class Card {
        private Config config;
        private Header header;
        private List<Element> elements;
    }
    @Data
    public static class Config {
        private Boolean wide_screen_mode;
        private Boolean enable_forward;
        private Boolean update_multi;
    }
    @Data
    public static class Element {
        private String tag;
        private Text text;
    }

    @Data
    public static class Text {
        private String content;
        private String tag;
    }

    @Data
    public static class Header {
        private String template;
        private Title title;
    }

    @Data
    public static class Title {
        private String content;
        private String tag;
    }

}

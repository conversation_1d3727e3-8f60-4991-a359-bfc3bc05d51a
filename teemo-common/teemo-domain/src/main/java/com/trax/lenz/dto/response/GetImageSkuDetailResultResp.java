package com.trax.lenz.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * GetImageSkuResultResp
 *
 * @author: jiaLiPan
 * @date: 2023-10-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetImageSkuDetailResultResp {

    private List<Image> imageList;

    @Data
    public static class Image {

        private String questionId;

        private String imageId;

        private String customerImageId;

        private String customerImageName;

        private Integer groupNo;

        private String customerGroupNo;

        private String imageUrl;

        private String isRemake;

        private String remakeScore;

        /**
         * 是否重复 0:否 1:是
         */
        private String repeat;

        /**
         * 重复组
         */
        private String repeatGroup;

        private Long height;

        private Long width;

        private List<Scene> sceneList;

        @Data
        public static class Scene {

            private String sceneId;

            private String sceneName;

            private List<GetImageSkuResultResp.CoordinateDTO> coordinateDTOList;

            private List<Product> productList;

        }

        @Data
        public static class Product {

            private Long productId;

            private String score;

            private String customerCode;

            private String productName;

            private String customProductName;

            private Integer layer;

            private Integer isFacing;

            private BigDecimal price;

            /**
             * 新拍拍赚宝洁定制字段, 是否盖住,true=盖住,默认是true盖住
             */
            private Boolean isMasked;

            /**
             * 新拍拍赚宝洁定制字段, 是否打勾,true=打勾,默认是false不打构
             */
            private Boolean isChecked;

            private List<GetImageSkuResultResp.CoordinateDTO> coordinateDTOList;

        }
    }
}

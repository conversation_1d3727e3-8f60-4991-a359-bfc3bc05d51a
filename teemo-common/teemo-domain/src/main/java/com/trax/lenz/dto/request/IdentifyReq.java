package com.trax.lenz.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 识别请求
 *
 * <AUTHOR>
 * @date 2022-03-28 11:13:22
 */
@Data
public class IdentifyReq {

    public static final String SUBMIT_MODE_INNER_TEEMO = "TEEMO";

    @ApiModelProperty(value = "任务id", required = true)
    private String taskId;

    @ApiModelProperty(value = "答卷id", required = true)
    private String responseId;

    @ApiModelProperty(value = "客户唯一Id", required = true)
    private String customerRequestId;

    @ApiModelProperty(value = "客户id", required = true)
    private String customerId;

    @ApiModelProperty(value = "是否为测试答卷（0.否 1.是）默认0")
    private Integer test;

    @ApiModelProperty(value = "提交方式")
    private String submitMode;

    @ApiModelProperty(value = "识别类型")
    private String type;

    @ApiModelProperty(value = "是否允许重复提交")
    private Boolean repeatSubmit;

    @ApiModelProperty(value = "teemo回调地址")
    private String callbackUrl;

    @ApiModelProperty(value = "图片组", required = true)
    private List<IdentifyReqGroup> groupList;

    @ApiModelProperty(value = "主数据")
    private IdentifyReqExtend extendInfo;

    @ApiModelProperty(value = "原始客户拓展主数据")
    private String extendInfoJson;
}

package com.trax.lenz.dto.request;

import lombok.Data;

import java.util.List;

/**
 * @ClassName IdentifyReqCoordinate
 * @Description:
 * <AUTHOR>
 * @Date 2022-12-22-17:13
 **/
@Data
public class IdentifyReqCoordinate {

    private String questionId;

    private List<Coordinate> coordinateList;
    @Data
    public static class Coordinate {
        private Integer rowNo;
        private Integer columnNo;
    }

}

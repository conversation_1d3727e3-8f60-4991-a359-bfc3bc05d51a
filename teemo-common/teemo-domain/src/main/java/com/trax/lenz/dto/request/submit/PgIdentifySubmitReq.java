package com.trax.lenz.dto.request.submit;

import lombok.Data;

import java.util.List;

/**
 * @ClassName PgIdentifySubimtReq
 * @Description:
 * <AUTHOR>
 * @Date 2024-07-25-16:33
 **/
@Data
public class PgIdentifySubmitReq {

    private String address;

    private String fromCaller;

    private String fromBusiness;

    private String responseId;

    private Boolean enableRemake;

    private Boolean enableVague;

    private Boolean enableAngle;

    private List<Image> imageList;

    @Data
    public static class Image {
        private String imageUrl;
        private String imageId;
    }
}

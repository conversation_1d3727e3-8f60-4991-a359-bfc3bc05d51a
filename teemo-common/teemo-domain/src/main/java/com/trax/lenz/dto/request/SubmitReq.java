package com.trax.lenz.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 提交请求
 *
 * <AUTHOR>
 * @date 2022-03-28 11:13:22
 */
@Data
public class SubmitReq {

    @ApiModelProperty(value = "任务id", required = true)
    private String taskId;

    @ApiModelProperty(value = "答卷id", required = true)
    private String responseId;

    @ApiModelProperty(value = "项目code", required = true)
    private String projectCode;

    @ApiModelProperty(value = "客户流水id", required = true)
    private String customerRequestId;

    @ApiModelProperty(value = "是否为测试答卷（0.否 1.是）默认0")
    private Integer test;

    private String submitUrl;

    private String callbackUrl;

    /**
     * 是否允许重复提交
     */
    private Boolean repeatSubmit;

    @ApiModelProperty(value = "请求body")
    private String body;

}

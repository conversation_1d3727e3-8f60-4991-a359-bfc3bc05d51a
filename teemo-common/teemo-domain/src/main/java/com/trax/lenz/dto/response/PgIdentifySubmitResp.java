package com.trax.lenz.dto.response;

import lombok.Data;

import java.util.List;

/**
 * @ClassName PgIdentifySubmitResp
 * @Description:
 * <AUTHOR>
 * @Date 2024-07-25-17:52
 **/
@Data
public class PgIdentifySubmitResp {

    private String responseId;

    private List<Result> resultList;

    @Data
    public static class Result {

        private String imageId;
        /**
         * 翻拍置信度
         */
        private String remakeScore;

        /**
         * 模糊度
         */
        private String vagueScore;

        /**
         * 倾斜角度
         */
        private String angle;
    }

}

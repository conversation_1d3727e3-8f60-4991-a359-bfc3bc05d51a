package com.trax.lenz.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 识别请求-图片
 *
 * <AUTHOR>
 * @date 2022-03-28 11:13:22
 */
@Data
public class IdentifyReqImage {

    @ApiModelProperty(value = "Trax图片id", required = true)
    private String traxImageId;

    @ApiModelProperty(value = "图片id", required = true)
    private String imageId;

    @ApiModelProperty(value = "图片类型（1.普通图 2.深度图）")
    private Integer imageType = 1;

    @ApiModelProperty(value = "图片名称", required = true)
    private String imageName;

    @ApiModelProperty(value = "图片url", required = true)
    private String imageUrl;

    @ApiModelProperty(value = "图片所在行下标（注意：拼接时使用，需考虑题目是否需要拼接，如不传后台默认从0*0排列）")
    private Integer rowNo;

    @ApiModelProperty(value = "图片所在行下标（注意：拼接时使用，需考虑题目是否需要拼接，如不传后台默认从0*0排列）")
    private Integer columnNo;

    @ApiModelProperty(value = "图片扩展信息（可传递水印等图片信息）")
    private IdentifyReqImageExtend imageExtendInfo;

}

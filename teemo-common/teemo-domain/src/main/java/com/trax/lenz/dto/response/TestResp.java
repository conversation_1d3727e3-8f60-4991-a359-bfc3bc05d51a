package com.trax.lenz.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class TestResp implements Serializable {

  private String tenantId;

  private Long id;

  @JSONField(format="yyyy-MM-dd HH:mm:ss")
  private Date createTime;

  private String createBy;

  @JSONField(format="yyyy-MM-dd HH:mm:ss")
  private Date updateTime;

  private String updateBy;


}

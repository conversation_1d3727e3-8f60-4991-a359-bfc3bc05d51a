package com.trax.lenz.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * GetImageSkuResultResp
 *
 * @author: jia<PERSON>iPan
 * @date: 2023-10-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetImageSkuResultResp {

    private List<Image> imageList;

    @Data
    public static class Image {

        private String questionId;

        private String imageId;

        private String customerImageId;

        private String imageUrl;

        private Integer maxSection;

        private Integer maxLayer;

        private Long height;

        private Long width;

        private Integer isRemake;

        private List<Scene> sceneList;

        @Data
        public static class Scene {

            private String sceneId;

            private String sceneName;

            private List<CoordinateDTO> coordinateDTOList;

        }

        private List<Product> productList;

        @Data
        public static class Product {

            private Long productId;

            private String productName;

            private String customProductName;

            private String barcode;

            private Integer total;

            private List<Patch> patchList;

            @Data
            public static class Patch {

                private Long productId;

                private List<CoordinateDTO> coordinateDTOList;

                private String label;

                private String sceneCode;

                private Integer section;

                private Integer layer;

                private Integer isFacing;

                private Integer patchType;

                private Float score;

            }
        }
    }

    @Data
    public static class CoordinateDTO {

        private Long x;

        private Long y;
    }
}

package com.trax.lenz.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 识别请求-拓展信息
 *
 * <AUTHOR>
 * @date 2022-03-28 11:13:22
 */
@Data
public class IdentifyReqExtend {

    @Schema(
            title = "门店编码",
            nullable = true,
            minLength = 1,
            maxLength = 32,
            defaultValue = "",
            description = "门店编码",
            example = "S2017121572349"
    )
    private String storeCode;

    @Schema(
            title = "门店名称",
            nullable = true,
            minLength = 1,
            maxLength = 32,
            defaultValue = "",
            description = "门店名称",
            example = "京龙超市"
    )
    private String storeName;

    @Schema(
            title = "门店地址",
            nullable = true,
            minLength = 1,
            maxLength = 128,
            defaultValue = "",
            description = "门店地址",
            example = "中国北京市朝阳区惠新东街17"
    )
    private String storeAddress;

    @Schema(
            title = "门店地址经纬度",
            nullable = true,
            minLength = 1,
            maxLength = 128,
            defaultValue = "",
            description = "门店地址经纬度",
            example = "30.29518 120.38888"
    )
    private String storeCoordinates;

    @Schema(
            title = "执行人Id",
            nullable = true,
            minLength = 1,
            maxLength = 32,
            defaultValue = "",
            description = "执行人Id",
            example = "002457"
    )
    private String executeId;

    @Schema(
            title = "执行账号",
            nullable = true,
            minLength = 1,
            maxLength = 32,
            defaultValue = "",
            description = "执行账号",
            example = "p00246"
    )
    private String executeAccount;

    @Schema(
            title = "执行人手机号",
            nullable = true,
            minLength = 1,
            maxLength = 11,
            defaultValue = "",
            example = "***********",
            description = "执行人手机号",
            pattern = "/^1[34578]\\d{9}$/"
    )
    private String executePhone;

    @Schema(
            title = "执行地址",
            nullable = true,
            minLength = 1,
            maxLength = 128,
            defaultValue = "",
            description = "执行地址",
            example = "中国北京市朝阳区惠新东街"
    )
    private String executeAddress;

    @Schema(
            title = "执行地址经纬度",
            nullable = true,
            minLength = 1,
            maxLength = 64,
            defaultValue = "",
            description = "执行地址经纬度",
            example = "{\"latitude\":29.364354,\"longitude\":113.16906}"
    )
    private String executeCoordinates;

    @Schema(
            title = "执行进店时间",
            nullable = true,
            example = "2022-10-11 12:20:00",
            description = "执行进店时间",
            pattern = "^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))\\\\s+([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$"
    )
    private String executeIntoTime;

    @Schema(
            title = "执行开始时间",
            nullable = true,
            example = "2022-10-11 12:20:00",
            description = "执行开始时间",
            pattern = "^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))\\\\s+([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$"
    )
    private String executeStartTime;

    @Schema(
            title = "执行结束时间",
            nullable = true,
            example = "2022-10-11 12:20:00",
            description = "执行结束时间",
            pattern = "^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))\\\\s+([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$"
    )
    private String executeEndTime;

    @Schema(
            title = "执行离店时间",
            nullable = true,
            example = "2022-10-11 12:20:00",
            description = "执行离店时间",
            pattern = "^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))\\\\s+([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$"
    )
    private String executeLeaveTime;

}

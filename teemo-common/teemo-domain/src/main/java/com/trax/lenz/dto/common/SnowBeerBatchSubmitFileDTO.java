package com.trax.lenz.dto.common;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName SnowBeerBatchSubmitFileDTO
 * @Description:
 * <AUTHOR>
 * @Date 2024-04-01-16:07
 **/

@Data
public class SnowBeerBatchSubmitFileDTO {
    /**
     * 营销中心
     */
    @ExcelProperty(value = "营销中心", index = 0)
    private String marketingCenter;
    /**
     * 销售大区
     */
    @ExcelProperty(value = "销售大区", index = 1)
    private String salesRegion;
    /**
     * 业务部
     */
    @ExcelProperty(value = "业务部", index = 2)
    private String businessSegment;
    /**
     * 工作站
     */
    @ExcelProperty(value = "工作站", index = 3)
    private String workstation;
    /**
     * 业务线
     */
    @ExcelProperty(value = "业务线", index = 4)
    private String serviceLine;
    /**
     * 照片链接
     */
    @ExcelProperty(value = "照片链接", index = 5)
    private String imageUrl;
    /**
     * 终端一级类型
     */
    @ExcelProperty(value = "终端一级类型", index = 6)
    private String terminalType;
    /**
     * 制高点管理分级
     */
    @ExcelProperty(value = "制高点管理分级", index = 7)
    private String manageScale;
    /**
     * 制高点档次分级
     */
    @ExcelProperty(value = "制高点档次分级", index = 8)
    private String manageGrade;
    /**
     * 连锁属性
     */
    @ExcelProperty(value = "连锁属性", index = 9)
    private String chainProperty;
    /**
     * 连锁公司
     */
    @ExcelProperty(value = "连锁公司", index = 10)
    private String chainCorporation;
    /**
     * 连锁品牌
     */
    @ExcelProperty(value = "连锁品牌", index = 11)
    private String chainBrand;
    /**
     * 终端状态
     */
    @ExcelProperty(value = "终端状态", index = 12)
    private String terminalState;
    /**
     * 终端编码
     */
    @ExcelProperty(value = "终端编码", index = 13)
    private String terminalCoding;
    /**
     * 终端名称
     */
    @ExcelProperty(value = "终端名称", index = 14)
    private String terminalName;
    /**
     * 一级照片类型
     */
    @ExcelProperty(value = "一级照片类型", index = 15)
    private String firstImgType;
    /**
     * 二级照片类型
     */
    @ExcelProperty(value = "二级照片类型", index = 16)
    private String secondImgType;
    /**
     * 三级照片类型
     */
    @ExcelProperty(value = "三级照片类型", index = 17)
    private String thirdImgType;
    /**
     * 拜访类型
     */
    @ExcelProperty(value = "拜访类型", index = 18)
    private String visitType;
    /**
     * 采集人员编码
     */
    @ExcelProperty(value = "采集人员编码", index = 19)
    private String personCode;
    /**
     * 采集人员姓名
     */
    @ExcelProperty(value = "采集人员姓名", index = 20)
    private String personName;
    /**
     * 终端业务员编码
     */
    @ExcelProperty(value = "终端业务员编码", index = 21)
    private String agentCode;
    /**
     * 终端业务员姓名
     */
    @ExcelProperty(value = "终端业务员姓名", index = 22)
    private String agentName;
    /**
     * 拜访单据号
     */
    @ExcelProperty(value = "拜访单据号", index = 23)
    private String visitCode;
    /**
     * 拜访日期
     */
    @ExcelProperty(value = "拜访日期", index = 24)
    private String visitDate;

}

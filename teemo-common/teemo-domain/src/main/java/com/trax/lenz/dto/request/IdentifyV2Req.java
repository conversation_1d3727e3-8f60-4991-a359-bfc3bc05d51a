package com.trax.lenz.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 识别请求V2
 *
 * <AUTHOR>
 * @date 2023-11-08 11:13:22
 */
@Data
public class IdentifyV2Req {

    public static final String SUBMIT_MODE_INNER_TEEMO = "TEEMO";

    @Schema(title = "任务id", required = true, minLength = 1, maxLength = 32, description = "答卷任务Id(taskId)", pattern = "^[A-Za-z0-9]+$", example = "20220225103752284997387703947264")
    private String taskId;

    @Schema(title = "答卷id", required = true, minLength = 1, maxLength = 32, description = "答卷id, 内部提交, rid由提交方生成, 不为空", pattern = "^[0-9]*$", example = "978314215541112832")
    private String responseId;

    @Schema(title = "合作伙伴业务参数", nullable = true, minLength = 1, maxLength = 128, description = "合作伙伴业务参数", defaultValue = "", example = "")
    private String customerRequestId;

    @Schema(title = "客户id", required = true, minLength = 1, maxLength = 32, description = "提交方标识", allowableValues = {"PPZ", "LENZ", "TEEMO", "dcs"})
    private String customerId;

    @Schema(title = "是否为测试答卷", nullable = true, minLength = 1, maxLength = 1, defaultValue = "0", allowableValues = {"0", "1"}, description = "是否为测试答卷, 0:否, 1:是", example = "0")
    private Integer test = 0;

    @Schema(title = "提交方式", required = true, minLength = 1, maxLength = 10, defaultValue = "INNER_PPZ", allowableValues = {"INNER_DCS", "INNER_RM", "INNER_PPZ", "INNER_LJT", "TEEMO"}, description = "提交方式, 根据实际调用方填写", example = "INNER_DCS")
    private String submitMode;

    @Schema(title = "是否允许重复提交", nullable = true, defaultValue = "false", example = "true", description = "布尔型字段描述")
    private Boolean repeatSubmit;

    @Schema(description = "答卷主数据", nullable = true)
    private IdentifyReqExtend extendInfo;
    /**
     * 提交方式
     */
    @Schema(title = "识别类型", nullable = true, minLength = 1, maxLength = 128, description = "")
    private String type;
    /**
     * 图片id
     */
    @Schema(title = "图片id", required = true, minLength = 1, maxLength = 128, description = "图片Id", example = "984056415017238535")
    private String imageId;
    /**
     * 图片url
     */
    @Schema(title = "图片url", required = true, minLength = 1, maxLength = 512, description = "图片Url", example = "https://lenz-prod-app.oss-cn-beijing.aliyuncs.com/AutoTestImage/AutoTestJZV2/iOS_3857_1646117724393.jpg")
    private String imageUrl;
    /**
     * 品类
     */
    private String businessName;
    /**
     * 模型名
     */
    private String modelName;
}

<?xml version="1.0" encoding="UTF-8"?>

<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <localRepository>/.m2/repo</localRepository>
  <pluginGroups></pluginGroups>
  <proxies></proxies>
  <servers>
    <server>
      <id>trax-release</id>
      <username>admin</username>
      <password>admin123</password>
    </server>
    <server>
      <id>lenz-snapshots</id>
      <username>admin</username>
      <password>admin123</password>
    </server>
  </servers>
  <mirrors>
   <mirror>
      <id>lenz-snapshots</id>
      <mirrorOf>lenz-snapshots</mirrorOf>
      <name>lenz-snapshots</name>
      <url>http://maven.ppznet.com:8082/repository/lenz-snapshots/</url>
    </mirror>

    <mirror>
      <id>trax-release</id>
      <mirrorOf>trax-release</mirrorOf>
      <name>trax-release</name>
      <url>http://maven.ppznet.com:8082/repository/trax-release/</url>
    </mirror>


    <mirror>
      <id>lenz-public</id>
      <mirrorOf>lenz-public</mirrorOf>
      <name>lenz-public</name>
      <url>http://maven.ppznet.com:8082/repository/maven-public/</url>
    </mirror>

    <mirror>
      <id>aliyun</id>
      <name>aliyun</name>
      <mirrorOf>*,!lenz</mirrorOf>
      <url>http://maven.aliyun.com/nexus/content/groups/public</url>
    </mirror>

  </mirrors>


  <profiles>
    <profile>
        <id>dev</id>
        <activation>
          <activeByDefault>true</activeByDefault>
        </activation>
        <repositories>
          <repository>
              <id>lenz-public</id>
              <url>http://maven.ppznet.com:8082/repository/maven-public/</url>
              <releases>
                <enabled>true</enabled>
              </releases>
              <snapshots>
                <enabled>true</enabled>
              </snapshots>
          </repository>
          <repository>
            <id>aliyun</id>
            <url>http://maven.aliyun.com/nexus/content/groups/public</url>
          </repository>
        </repositories>

        <pluginRepositories>
          <pluginRepository>
            <id>aliyun</id>
            <url>http://maven.aliyun.com/nexus/content/groups/public</url>
            <releases>
                  <enabled>true</enabled>
              </releases>
              <snapshots>
                  <enabled>false</enabled>
              </snapshots>
          </pluginRepository>
        </pluginRepositories>
    </profile>

    <!-- 配置默认jdk环境 -->
    <profile>
      <id>jdk1.7</id>
      <activation>
        <activeByDefault>false</activeByDefault>
        <jdk>1.7</jdk>
      </activation>
      <properties>
        <maven.compiler.source>1.7</maven.compiler.source>
        <maven.compiler.target>1.7</maven.compiler.target>
        <maven.compiler.compilerVersion>1.7</maven.compiler.compilerVersion>
      </properties>
    </profile>
    <profile>
      <id>jdk1.8</id>
      <activation>
        <activeByDefault>true</activeByDefault>
        <jdk>1.8</jdk>
      </activation>
      <properties>
        <JAVA_HOME>/data/java/jdk1.8.0_181</JAVA_HOME>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
      </properties>
    </profile>
  </profiles>

  <activeProfiles>
    <activeProfile>dev</activeProfile>
  </activeProfiles>

</settings>

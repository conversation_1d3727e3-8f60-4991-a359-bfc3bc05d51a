<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <artifactId>lenz-parent</artifactId>
    <groupId>com.trax.lenz.common</groupId>
    <version>2.0.46</version>
  </parent>

  <groupId>com.trax.lenz</groupId>
  <artifactId>teemo-parent</artifactId>
  <packaging>pom</packaging>
  <version>1.0.70</version>

  <properties>
    <lenz.version>2.0.46</lenz.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <spring-boot.version>2.5.0</spring-boot.version>
    <spring-cloud.version>2020.0.2</spring-cloud.version>
    <spring-cloud-alibaba.version>2021.1</spring-cloud-alibaba.version>
    <knife4j.version>3.0.3</knife4j.version>
    <druid.version>1.2.6</druid.version>
    <mysql-connector.version>5.1.47</mysql-connector.version>
    <dynamic-datasource.version>3.3.2</dynamic-datasource.version>
    <sharding-jdbc.version>4.1.1</sharding-jdbc.version>
    <mybatis.plus.version>3.2.0</mybatis.plus.version>
    <scm.url>https://gitlab.langjtech.com/rd-java/teemo-parent.git</scm.url>
  </properties>

  <!-- ?��????? scm ???? -->
  <scm>
    <connection>scm:git:${scm.url}</connection>
    <developerConnection>scm:git:${scm.url}</developerConnection>
  </scm>

  <repositories>
    <repository>
      <id>lenz-public</id>
      <url>http://maven.ppznet.com:8082/repository/maven-public/</url>
    </repository>
  </repositories>

  <pluginRepositories>
    <pluginRepository>
      <id>lenz-public</id>
      <url>http://maven.ppznet.com:8082/repository/maven-public/</url>
      <releases>
      </releases>
      <snapshots>
      </snapshots>
    </pluginRepository>
  </pluginRepositories>

  <dependencyManagement>
    <dependencies>

      <!-- SpringCloud ????? -->
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- SpringCloud Alibaba ????? -->
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-dependencies</artifactId>
        <version>${spring-cloud-alibaba.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- SpringBoot ???????? -->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- Lenz ???????????? -->
      <dependency>
        <groupId>com.trax.lenz.common</groupId>
        <artifactId>common-dependencies</artifactId>
        <version>${lenz.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-starter</artifactId>
        <version>${druid.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.4.3</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        <version>${dynamic-datasource.version}</version>
      </dependency>

      <dependency>
        <groupId>com.trax.lenz</groupId>
        <artifactId>teemo-domain</artifactId>
        <version>${project.version}</version>
      </dependency>



      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>${mysql-connector.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis.plus.version}</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-extension</artifactId>
        <version>3.4.3</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-generator</artifactId>
        <version>3.4.1</version>
      </dependency>

      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        <version>${dynamic-datasource.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.shardingsphere</groupId>
        <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
        <version>${sharding-jdbc.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${maven.pmd.version}</version>
        <configuration>
          <sourceEncoding>${project.build.sourceEncoding}</sourceEncoding>
          <minimumPriority>1</minimumPriority>
          <printFailingErrors>true</printFailingErrors>
          <rulesets>
            <ruleset>rulesets/java/ali-comment.xml</ruleset>
            <ruleset>rulesets/java/ali-concurrent.xml</ruleset>
            <ruleset>rulesets/java/ali-constant.xml</ruleset>
            <ruleset>rulesets/java/ali-exception.xml</ruleset>
            <ruleset>rulesets/java/ali-flowcontrol.xml</ruleset>
            <ruleset>rulesets/java/ali-naming.xml</ruleset>
            <ruleset>rulesets/java/ali-oop.xml</ruleset>
            <ruleset>rulesets/java/ali-orm.xml</ruleset>
            <ruleset>rulesets/java/ali-other.xml</ruleset>
            <ruleset>rulesets/java/ali-set.xml</ruleset>
          </rulesets>
          <excludeRoots>
            <excludeRoot>${basedir}</excludeRoot>
          </excludeRoots>
        </configuration>
        <executions>
          <!-- ??pmd:check??verify???????? -->
          <execution>
            <id>pmd-check-verify</id>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
          <!-- ??pmd:pmd??package???????? -->
          <execution>
            <id>pmd-pmd-package</id>
            <phase>package</phase>
            <goals>
              <goal>pmd</goal>
            </goals>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>com.alibaba.p3c</groupId>
            <artifactId>p3c-pmd</artifactId>
            <version>1.3.6</version>
          </dependency>
        </dependencies>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.5.3</version>
        <configuration>
          <autoVersionSubmodules>true</autoVersionSubmodules>
          <generateReleasePoms>false</generateReleasePoms>
          <arguments>-DskipTests</arguments>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <encoding>${project.build.sourceEncoding}</encoding>
        </configuration>
      </plugin>

    </plugins>
  </build>

<modules>  <module>teemo-common</module>
    <module>teemo-web</module>
  </modules>
</project>

# Redis 计数器功能实现总结

## 问题描述
需要实现一个 Redis 计数器功能，包括：
1. 设置初始值和过期时间
2. 计数器自增
3. 查询当前值

## 解决方案
创建了 `RedisCounterUtil` 工具类，提供以下功能：
1. `setCounter`: 设置计数器初始值和过期时间
2. `incrementCounter`: 计数器自增
3. `getCounter`: 查询当前值

## 实现特点
1. 使用 Spring 组件，支持依赖注入
2. 所有操作都是原子性的
3. 支持设置过期时间
4. 使用前缀避免键冲突
5. 支持分布式环境

## 使用示例
```java
@Resource
private RedisCounterUtil redisCounterUtil;

// 设置计数器
redisCounterUtil.setCounter("myCounter", 0, 3600);

// 增加计数器
long newValue = redisCounterUtil.incrementCounter("myCounter");

// 获取当前值
long currentValue = redisCounterUtil.getCounter("myCounter");
``` 
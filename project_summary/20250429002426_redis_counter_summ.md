# Redis 计数器工具类改造

## 问题描述
RedisCounterUtil 类需要使用另一套 Redis 连接地址信息，而不是使用默认的 Redis 连接。

## 解决方案
1. 创建了新的 Redis 配置属性类 `CounterRedisProperties`，用于配置另一套 Redis 连接信息
2. 创建了新的 Redis 配置类 `CounterRedisConfig`，用于配置另一套 Redis 连接
3. 修改了 `RedisCounterUtil` 类，使其使用新的 Redis 连接
4. 添加了连接池配置，提高 Redis 连接的性能和稳定性

## 修改内容
1. 新增 `CounterRedisProperties` 类，用于配置另一套 Redis 连接信息
2. 新增 `CounterRedisConfig` 类，用于配置另一套 Redis 连接
3. 修改 `RedisCounterUtil` 类，使其使用新的 Redis 连接
4. 添加了连接池配置，包括：
   - max-active: 300 (连接池最大连接数)
   - max-wait: 60 (连接池最大阻塞等待时间)
   - max-idle: 100 (连接池中的最大空闲连接)
   - min-idle: 20 (连接池中的最小空闲连接)

## 使用方法
1. 在配置文件中添加 `spring.redis-counter` 相关配置，包括连接信息和连接池配置
2. 使用 `RedisCounterUtil` 类进行计数器操作

## 注意事项
1. 需要确保新的 Redis 连接信息正确配置
2. 需要确保新的 Redis 连接可用
3. 连接池配置可以根据实际需求调整 
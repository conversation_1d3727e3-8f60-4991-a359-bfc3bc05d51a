# 工程结构
- teemo-common
    - teemo-api【用户服务RPC接口】
    - teemo-domain【RPC接口DTO】
    - teemo-business【持久化业务包】
- teemo-web
    - teemo-service     【微服务】
    - teemo-manager     【管理服务】 
# 工程介绍

# 功能特性
## Redis 计数器
提供了基于 Redis 的计数器功能，支持：
- 设置计数器初始值和过期时间
- 计数器自增
- 查询当前值
详细说明请参考：`project_summary/20240428120800_redis_counter_summ.md`

## Redis 计数器（独立连接）
提供了基于独立 Redis 连接的计数器功能，支持：
- 使用另一套 Redis 连接地址信息
- 设置计数器初始值和过期时间
- 计数器自增
- 查询当前值
详细说明请参考：`project_summary/20250429002426_redis_counter_summ.md`
    
# 版本记录

1. 初始化
2. 添加 Redis 计数器功能
3. 添加 Redis 计数器（独立连接）功能


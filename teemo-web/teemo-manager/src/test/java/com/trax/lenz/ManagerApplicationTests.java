package com.trax.lenz;

import com.trax.lenz.common.core.id.SnowFlakeFactory;
import com.trax.lenz.service.IdentifyService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class ManagerApplicationTests {

    @Autowired
    private IdentifyService identifyService;

    @Autowired
    private SnowFlakeFactory snowFlakeFactory;

    @Test
    public void test() {
        log.info(identifyService.getEnv());
        log.info(snowFlakeFactory.nextIdStr());
    }
}

package com.trax.lenz.enums;

import com.trax.lenz.common.core.id.SnowBusinessCode;

/**
 * 业务编码
 * <AUTHOR>
 */

public enum SnowCode implements SnowBusinessCode {
    TK("TK", "任务模块"),
    CO("CO", "企业模块"),
    ;
    private final String code;
    private final String info;

    SnowCode(String code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

}

package com.trax.lenz.factory;

import com.trax.lenz.common.core.exception.BusinessException;
import com.trax.lenz.http.RestCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * 消息工厂，支持国际化
 *
 * <AUTHOR>
 */
@Component
public class MessageFactory {

  private static MessageSource source;

  /**
   * 在Spring里，静态变量/类变量不是对象的属性，而是一个类的属性，不能用@Autowired一个静态变量（对象），使之成为一个SpringBean。<br />
   * 只能通过setter方法注入，并把类注解成为组件
   *
   * @param source
   */
  @Autowired
  public void init(MessageSource source) {
    MessageFactory.source = source;
  }

  /**
   * 抛出校验错误异常
   * @param args
   */
  public void restErrMsg(RestCode restCode, Object... args) {
    // 消息的参数化和国际化配置
    Locale locale = LocaleContextHolder.getLocale();
    String msg = source.getMessage(restCode.getMsgKey(), args, locale);
    throw new BusinessException(msg,restCode.getCode());
  }

  /**
   * 获取国际化信息内容
   * @param msgKey
   * @param args
   */
  public String msg(String msgKey, Object... args) {
    // 消息的参数化和国际化配置
    Locale locale = LocaleContextHolder.getLocale();
    String msg = source.getMessage(msgKey, args, locale);
    return msg;
  }

}

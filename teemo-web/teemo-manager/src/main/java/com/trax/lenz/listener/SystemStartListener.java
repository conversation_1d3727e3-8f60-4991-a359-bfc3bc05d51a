package com.trax.lenz.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 启动监听
 * <AUTHOR>
 */
@Component
@Slf4j
public class SystemStartListener implements ApplicationRunner {

    @Override
    public void run(ApplicationArguments args) {
      log.info("启动后做一些事情");
    }

}

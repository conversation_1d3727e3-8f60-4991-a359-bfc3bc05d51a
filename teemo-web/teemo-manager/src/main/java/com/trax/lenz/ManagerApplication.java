package com.trax.lenz;

import com.alibaba.druid.support.http.StatViewServlet;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 */
@Slf4j
@EnableCaching
@EnableConfigurationProperties
@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, UserDetailsServiceAutoConfiguration.class})
@MapperScan("com.trax.lenz.mapper")
public class ManagerApplication {

  public static void main(String[] args) {
    SpringApplication.run(ManagerApplication.class, args);
    log.info("=========== 框架服务启动成功 ===========");
  }
  
  @Bean
  public ServletRegistrationBean<StatViewServlet> druidStatViewServlet() {
    ServletRegistrationBean<StatViewServlet> registrationBean = new ServletRegistrationBean<>(new StatViewServlet(),  "/druid/*");
    registrationBean.addInitParameter("allow", "**************");// IP白名单 (没有配置或者为空，则允许所有访问)
    registrationBean.addInitParameter("deny", "");// IP黑名单 (存在共同时，deny优先于allow)
    registrationBean.addInitParameter("loginUsername", "teemo");
    registrationBean.addInitParameter("loginPassword", "1234");
    registrationBean.addInitParameter("resetEnable", "false");
    return registrationBean;
  }

}

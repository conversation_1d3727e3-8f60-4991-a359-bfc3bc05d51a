package com.trax.lenz.controller;

import com.trax.lenz.api.dto.response.GetAiResultLatestResp;
import com.trax.lenz.api.dto.response.ResponseData;
import com.trax.lenz.service.CallbackService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 业务入口Controller
 *
 * <AUTHOR>
 * @date 2022-09-09 17:03:48
 */
@Slf4j
@RestController
@RequestMapping("/business")
public class BusinessController {

    private CallbackService callbackService;

    public BusinessController(CallbackService callbackService) {
        this.callbackService = callbackService;
    }

    /**
     * 回调
     *
     * @param resp
     * @return ResponseData<>
     */
    @RequestMapping(value = "/callback", method = RequestMethod.GET)
    @ApiOperation(value = "回调")
    public ResponseData callback(@RequestBody GetAiResultLatestResp resp) {
        try {
            callbackService.resultCallback(resp);
            return ResponseData.success();
        } catch (Exception e) {
            log.error("回调异常!", e);
            return ResponseData.failure();
        }
    }
}

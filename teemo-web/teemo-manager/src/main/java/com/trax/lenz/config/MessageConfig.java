package com.trax.lenz.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;

import java.nio.charset.StandardCharsets;

/**
 * 国际化消息配置
 * <AUTHOR>
 */
@Configuration
public class MessageConfig {

  /** 国际化文件路径 */
  @Value("${spring.messages.basename:i18n/messages/messages}")
  public String basename;

  /**
   * 用于解析消息的策略接口，支持这些消息的参数化和国际化。
   * @return
   */
  @Bean
  public MessageSource messageSource() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setBasename(basename);
    messageSource.setDefaultEncoding(StandardCharsets.UTF_8.name());
    return messageSource;
  }

}

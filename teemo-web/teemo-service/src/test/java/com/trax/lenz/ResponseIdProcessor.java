package com.trax.lenz;

import com.trax.lenz.common.utils.HttpUtil;
import com.trax.lenz.dto.HttpResponse;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * ResponseId处理器
 * 读取txt文件中的response_id，查询数据库并调用HTTP接口
 */
public class ResponseIdProcessor {

    // TODO: 请根据实际情况修改以下配置
    private static final String DB_URL = "**************************************************************";
    private static final String DB_USERNAME = "gabriel_username";
    private static final String DB_PASSWORD = "3Ciz6bCyrU";
    private static final String DB_DRIVER = "com.p6spy.engine.spy.P6SpyDriver";
    
    // ds2库配置 - 新增
    private static final String DB2_URL = "***********************************************************";
    private static final String DB2_USERNAME = "teemo_username";
    private static final String DB2_PASSWORD = "R1P3jMHok2ipQi8E";

    private static final String HTTP_URL = "https://gateway-service.langjtech.com/teemo/business/appeal/callback";
    private static final String HTTP_METHOD = "POST"; // 或 GET

    private static final String INPUT_FILE_PATH = "D://retry/1.txt";
    private static final String LOG_FILE_PATH = "D://retry/process_log.txt";

    public static void main(String[] args) {
        ResponseIdProcessor processor = new ResponseIdProcessor();
        processor.processResponseIds();
    }

    /**
     * 主处理方法
     */
    public void processResponseIds() {
        List<String> responseIds = readResponseIdsFromFile(INPUT_FILE_PATH);
        if (responseIds.isEmpty()) {
            System.out.println("文件为空或读取失败");
            return;
        }

        System.out.println("开始处理 " + responseIds.size() + " 个response_id");

        // 初始化数据库连接
        try (Connection connection = getDatabaseConnection()) {

            for (int i = 0; i < responseIds.size(); i++) {
                String responseId = responseIds.get(i);
                System.out.println("处理第 " + (i + 1) + " 个: " + responseId);

                try {
                    // 查询数据库
                    String respContent = queryAppealTable(connection, responseId);

                    if (respContent != null) {
                        // 调用HTTP接口
                        boolean httpSuccess = callHttpApi(responseId, respContent);

                        // 记录日志
                        logProcessResult(responseId, respContent, httpSuccess);

                        if (httpSuccess) {
                            System.out.println("✓ " + responseId + " 处理成功");
                        } else {
                            System.out.println("✗ " + responseId + " HTTP调用失败");
                        }
                    } else {
                        System.out.println("✗ " + responseId + " 数据库查询无结果");
                        logProcessResult(responseId, null, false);
                    }

                    // 添加延迟，避免请求过于频繁
                    TimeUnit.MILLISECONDS.sleep(100);

                } catch (Exception e) {
                    System.err.println("处理 " + responseId + " 时发生错误: " + e.getMessage());
                    logProcessResult(responseId, null, false);
                }
            }

        } catch (Exception e) {
            System.err.println("数据库连接失败: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("所有response_id处理完成");
    }

    /**
     * 从文件读取response_id列表
     */
    private List<String> readResponseIdsFromFile(String filePath) {
        List<String> responseIds = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    responseIds.add(line);
                }
            }
        } catch (IOException e) {
            System.err.println("读取文件失败: " + e.getMessage());
        }

        return responseIds;
    }

    /**
     * 获取数据库连接
     */
    private Connection getDatabaseConnection() throws SQLException, ClassNotFoundException {
        Class.forName(DB_DRIVER);
        return DriverManager.getConnection(DB_URL, DB_USERNAME, DB_PASSWORD);
    }

    /**
     * 查询appeal表获取resp_content
     */
    private String queryAppealTable(Connection connection, String responseId) throws SQLException {
        String sql = "SELECT resp_content FROM appeal WHERE response_id = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, responseId);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("resp_content");
                }
            }
        }

        return null;
    }

    /**
     * 调用HTTP接口 - 修改：添加ds2库检查逻辑
     */
    private boolean callHttpApi(String responseId, String respContent) {
        try {
            // 新增：首先检查ds2库appeal_request表的callback_param字段
            if (isCallbackParamExists(responseId)) {
                System.out.println("✓ " + responseId + " 已存在callback_param，跳过回调");
                return true; // 返回true表示处理成功（跳过回调）
            }
            
            // 执行HTTP调用
            HttpResponse httpResponse = HttpUtil.post(HTTP_URL, respContent, null);
            return true;
        } catch (Exception e) {
            System.err.println("HTTP调用失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 新增：检查ds2库appeal_request表的callback_param字段是否存在值
     */
    private boolean isCallbackParamExists(String responseId) {
        try (Connection connection = getDatabase2Connection()) {
            String sql = "SELECT callback_param FROM appeal_request WHERE response_id = ?";
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setString(1, responseId);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        String callbackParam = rs.getString("callback_param");
                        return callbackParam != null && !callbackParam.trim().isEmpty();
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("检查callback_param失败: " + e.getMessage());
        }
        
        return false; // 如果查询失败或没有记录，默认返回false
    }
    
    /**
     * 新增：获取ds2数据库连接
     */
    private Connection getDatabase2Connection() throws SQLException, ClassNotFoundException {
        Class.forName(DB_DRIVER);
        return DriverManager.getConnection(DB2_URL, DB2_USERNAME, DB2_PASSWORD);
    }

    /**
     * 记录处理结果到日志文件
     */
    private void logProcessResult(String responseId, String respContent, boolean success) {
        try (FileWriter writer = new FileWriter(LOG_FILE_PATH, true);
             BufferedWriter bw = new BufferedWriter(writer);
             PrintWriter out = new PrintWriter(bw)) {
            String timestamp = new Date().toString();
            String status = success ? "SUCCESS" : "FAILED";
            String content = respContent != null ? respContent : "NO_DATA";

            out.println(String.format("[%s] %s | %s | %s", timestamp, responseId, status, content));

        } catch (IOException e) {
            System.err.println("写入日志失败: " + e.getMessage());
        }
    }
}

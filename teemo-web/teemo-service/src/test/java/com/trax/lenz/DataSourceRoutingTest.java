package com.trax.lenz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trax.lenz.entity.AppealRequest;
import com.trax.lenz.entity.BatchIdentifyRequest;
import com.trax.lenz.entity.CheckRepeatRequest;
import com.trax.lenz.entity.IdentifyRequest;
import com.trax.lenz.service.AppealRequestService;
import com.trax.lenz.service.IBatchIdentifyRequestService;
import com.trax.lenz.service.IdentifyRequestService;
import com.trax.lenz.service.impl.CheckRepeatRequestServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 数据源路由测试类
 * 用于测试不同配置下表在多个数据源环境下的路由情况
 * 
 * 测试场景：
 * 1. 未分库分表的表（AppealRequest、BatchIdentifyRequest、CheckRepeatRequest）
 *    - 受 default-data-source-name 配置影响
 *    - 没有配置时会随机路由，可能导致查询失败
 *    - 配置后会固定路由到指定数据源
 * 
 * 2. 分库分表的表（IdentifyRequest）
 *    - 不受 default-data-source-name 配置影响
 *    - 有明确的分片规则，按 response_id 路由
 *    - 查询结果应该始终稳定
 * 
 * 测试数据说明：
 * - 未分库分表表：使用在 ds0 中存在但在 ds1 中不存在的数据进行测试
 * - 分库分表表：使用按分片规则存在的数据进行测试
 * - 查询100次，统计查询结果为null的次数
 * - 如果配置正确，未分库分表表查询结果应该始终不为null
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class DataSourceRoutingTest {

    @Autowired
    private AppealRequestService appealRequestService;

    @Autowired
    private IBatchIdentifyRequestService batchIdentifyRequestService;

    @Autowired
    private CheckRepeatRequestServiceImpl checkRepeatRequestService;

    @Autowired
    private IdentifyRequestService identifyRequestService;

    /**
     * 综合测试所有表的数据源路由
     * 合并所有表的测试逻辑，便于统一查看结果
     * 
     * 测试说明：
     * 1. 未分库分表的表（AppealRequest、BatchIdentifyRequest、CheckRepeatRequest）
     *    - 受 default-data-source-name 配置影响
     *    - 没有配置时会随机路由，可能导致查询失败
     *    - 配置后会固定路由到指定数据源
     * 
     * 2. 分库分表的表（IdentifyRequest）
     *    - 不受 default-data-source-name 配置影响
     *    - 有明确的分片规则，按 response_id 路由
     *    - 查询结果应该始终稳定
     */
    @Test
    public void testAllTablesDataSourceRouting() {

        
        int totalQueries = 100;
        
        // 测试数据配置
        long testAppealId = 1349260197004115968L;
        String testResponseGroupId = "3074866662265847808";
        String testCheckRepeatId = "2710406186108583938";
        String testIdentifyResponseId = "3976066833805869056"; // 分库分表测试数据
        
        // 测试结果统计
        int appealNullCount = 0;
        int batchNullCount = 0;
        int checkNullCount = 0;
        int identifyNullCount = 0;
        

        
        // 执行测试
        for (int i = 0; i < totalQueries; i++) {
            // 测试 AppealRequest
            LambdaQueryWrapper<AppealRequest> appealWrapper = new LambdaQueryWrapper<>();
            appealWrapper.eq(AppealRequest::getAppealId, testAppealId);
            AppealRequest appealResult = appealRequestService.getOne(appealWrapper);
            if (appealResult == null) {
                appealNullCount++;
            }
            
            // 测试 BatchIdentifyRequest
            LambdaQueryWrapper<BatchIdentifyRequest> batchWrapper = new LambdaQueryWrapper<>();
            batchWrapper.eq(BatchIdentifyRequest::getResponseGroupId, testResponseGroupId);
            BatchIdentifyRequest batchResult = batchIdentifyRequestService.getOne(batchWrapper);
            if (batchResult == null) {
                batchNullCount++;
            }
            
            // 测试 CheckRepeatRequest
            LambdaQueryWrapper<CheckRepeatRequest> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(CheckRepeatRequest::getCheckRepeatId, testCheckRepeatId);
            CheckRepeatRequest checkResult = checkRepeatRequestService.getOne(checkWrapper);
            if (checkResult == null) {
                checkNullCount++;
            }
            
            // 测试 IdentifyRequest（分库分表）
            LambdaQueryWrapper<IdentifyRequest> identifyWrapper = new LambdaQueryWrapper<>();
            identifyWrapper.eq(IdentifyRequest::getResponseId, testIdentifyResponseId);
            IdentifyRequest identifyResult = identifyRequestService.getOne(identifyWrapper);
            if (identifyResult == null) {
                identifyNullCount++;
            }
        }

        // 输出测试结果
        log.info("============================================================");
        log.info("开始综合测试所有表的数据源路由...");
        log.info("测试配置：");
        log.info("【未分库分表表】- 受 default-data-source-name 配置影响：");
        log.info("- AppealRequest 测试ID: {}", testAppealId);
        log.info("- BatchIdentifyRequest 测试ID: {}", testResponseGroupId);
        log.info("- CheckRepeatRequest 测试ID: {}", testCheckRepeatId);
        log.info("【分库分表表】- 不受 default-data-source-name 配置影响：");
        log.info("- IdentifyRequest 测试ID: {}", testIdentifyResponseId);
        log.info("- 每个表查询次数: {}", totalQueries);
        log.info("开始执行测试...");
        log.info("数据源路由测试结果汇总");
        log.info("============================================================");
        
        // AppealRequest 结果
        double appealSuccessRate = (totalQueries - appealNullCount) * 100.0 / totalQueries;
        log.info("AppealRequest 表：");
        log.info("  总查询次数: {}", totalQueries);
        log.info("  null结果次数: {}", appealNullCount);
        log.info("  查询成功率: {}%", String.format("%.2f", appealSuccessRate));
        log.info("  状态: {}", appealNullCount == 0 ? "✅ 正常" : "⚠️ 异常");
        
        // BatchIdentifyRequest 结果
        double batchSuccessRate = (totalQueries - batchNullCount) * 100.0 / totalQueries;
        log.info("BatchIdentifyRequest 表：");
        log.info("  总查询次数: {}", totalQueries);
        log.info("  null结果次数: {}", batchNullCount);
        log.info("  查询成功率: {}%", String.format("%.2f", batchSuccessRate));
        log.info("  状态: {}", batchNullCount == 0 ? "✅ 正常" : "⚠️ 异常");
        
        // CheckRepeatRequest 结果
        double checkSuccessRate = (totalQueries - checkNullCount) * 100.0 / totalQueries;
        log.info("CheckRepeatRequest 表：");
        log.info("  总查询次数: {}", totalQueries);
        log.info("  null结果次数: {}", checkNullCount);
        log.info("  查询成功率: {}%", String.format("%.2f", checkSuccessRate));
        log.info("  状态: {}", checkNullCount == 0 ? "✅ 正常" : "⚠️ 异常");
        
        // IdentifyRequest 结果（分库分表）
        double identifySuccessRate = (totalQueries - identifyNullCount) * 100.0 / totalQueries;
        log.info("IdentifyRequest 表（分库分表）：");
        log.info("  总查询次数: {}", totalQueries);
        log.info("  null结果次数: {}", identifyNullCount);
        log.info("  查询成功率: {}%", String.format("%.2f", identifySuccessRate));
        log.info("  状态: {}", identifyNullCount == 0 ? "✅ 正常" : "⚠️ 异常");
        log.info("  说明: 分库分表表不受 default-data-source-name 配置影响");
        
        // 总体评估
        log.info("============================================================");
        int totalNullCount = appealNullCount + batchNullCount + checkNullCount;
        int totalQueriesAll = totalQueries * 3;
        double overallSuccessRate = (totalQueriesAll - totalNullCount) * 100.0 / totalQueriesAll;
        
        log.info("【未分库分表表】总体评估：");
        log.info("  总查询次数: {}", totalQueriesAll);
        log.info("  总null结果次数: {}", totalNullCount);
        log.info("  总体成功率: {}%", String.format("%.2f", overallSuccessRate));
        
        if (totalNullCount == 0) {
            log.info("🎉 未分库分表表的数据源路由配置都正确！");
        } else {
            log.warn("⚠️ 未分库分表表存在数据源路由问题，建议检查 default-data-source-name 配置");
        }
        
        // 分库分表表评估
        log.info("【分库分表表】评估：");
        log.info("  IdentifyRequest 查询次数: {}", totalQueries);
        log.info("  IdentifyRequest null结果次数: {}", identifyNullCount);
        log.info("  IdentifyRequest 成功率: {}%", String.format("%.2f", identifySuccessRate));
        
        if (identifyNullCount == 0) {
            log.info("✅ 分库分表表查询正常，符合预期（不受 default-data-source-name 影响）");
        } else {
            log.warn("⚠️ 分库分表表查询异常，可能存在数据问题或分片配置问题");
        }
        
        log.info("============================================================");
        log.info("测试完成");
    }

    /**
     * 测试数据准备方法
     * 用于在测试前准备测试数据（可选）
     * 注意：这个方法需要根据实际业务需求进行调整
     */
    @Test
    public void prepareTestData() {
        log.info("准备测试数据...");
        
        // 这里可以添加创建测试数据的逻辑
        // 例如：在 ds0 中插入一些测试数据，确保这些数据在 ds1 中不存在
        
        log.info("测试数据准备完成");
        log.info("请确保测试数据在 ds0 中存在但在 ds1 中不存在");
        log.info("然后运行其他测试方法进行验证");
    }

    /**
     * 调试分库分表表查询问题
     * 用于排查 IdentifyRequest 表查询失败的原因
     */
    @Test
    public void debugIdentifyRequestQuery() {
        log.info("开始调试 IdentifyRequest 表查询问题...");
        
        String testIdentifyResponseId = "3702217176393187328";
        
        log.info("测试数据：responseId = {}", testIdentifyResponseId);
        
        // 尝试不同的查询方式
        try {
            // 方式1：使用 getOne
            LambdaQueryWrapper<IdentifyRequest> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(IdentifyRequest::getResponseId, testIdentifyResponseId);
            IdentifyRequest result1 = identifyRequestService.getOne(wrapper1);
            log.info("方式1 - getOne 查询结果: {}", result1 != null ? "成功" : "失败");
            
            // 方式2：使用 list
            LambdaQueryWrapper<IdentifyRequest> wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(IdentifyRequest::getResponseId, testIdentifyResponseId);
            java.util.List<IdentifyRequest> result2 = identifyRequestService.list(wrapper2);
            log.info("方式2 - list 查询结果: 找到 {} 条记录", result2.size());
            
            // 方式3：使用 count
            LambdaQueryWrapper<IdentifyRequest> wrapper3 = new LambdaQueryWrapper<>();
            wrapper3.eq(IdentifyRequest::getResponseId, testIdentifyResponseId);
            long count = identifyRequestService.count(wrapper3);
            log.info("方式3 - count 查询结果: {} 条记录", count);
            
            // 方式4：尝试查询其他 responseId
            String[] testIds = {"3257927273753870342", "3709125888986841088", "3791247828350140416"};
            for (String testId : testIds) {
                LambdaQueryWrapper<IdentifyRequest> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(IdentifyRequest::getResponseId, testId);
                long testCount = identifyRequestService.count(wrapper);
                log.info("测试 responseId {}: 找到 {} 条记录", testId, testCount);
            }
            
        } catch (Exception e) {
            log.error("查询过程中发生异常: ", e);
        }
        
        log.info("调试完成");
    }

    /**
     * 验证测试数据是否存在
     * 用于在运行测试前验证测试数据是否正确准备
     */
    @Test
    public void verifyTestData() {
        log.info("验证测试数据...");
        
        // 测试数据配置（与主测试方法保持一致）
        long testAppealId = 1349260197004115968L;
        String testResponseGroupId = "3074866662265847808";
        String testCheckRepeatId = "2710406186108583938";
        String testIdentifyResponseId = "3702217176393187328";
        
        log.info("==================================================");
        log.info("测试数据验证结果");
        log.info("==================================================");
        
        log.info("【未分库分表表】测试数据验证：");
        // 验证 AppealRequest 测试数据
        LambdaQueryWrapper<AppealRequest> appealWrapper = new LambdaQueryWrapper<>();
        appealWrapper.eq(AppealRequest::getAppealId, testAppealId);
        AppealRequest appealResult = appealRequestService.getOne(appealWrapper);
        log.info("AppealRequest (ID: {}): {}", testAppealId, appealResult != null ? "✅ 存在" : "❌ 不存在");
        
        // 验证 BatchIdentifyRequest 测试数据
        LambdaQueryWrapper<BatchIdentifyRequest> batchWrapper = new LambdaQueryWrapper<>();
        batchWrapper.eq(BatchIdentifyRequest::getResponseGroupId, testResponseGroupId);
        BatchIdentifyRequest batchResult = batchIdentifyRequestService.getOne(batchWrapper);
        log.info("BatchIdentifyRequest (ID: {}): {}", testResponseGroupId, batchResult != null ? "✅ 存在" : "❌ 不存在");
        
        // 验证 CheckRepeatRequest 测试数据
        LambdaQueryWrapper<CheckRepeatRequest> checkWrapper = new LambdaQueryWrapper<>();
        checkWrapper.eq(CheckRepeatRequest::getCheckRepeatId, testCheckRepeatId);
        CheckRepeatRequest checkResult = checkRepeatRequestService.getOne(checkWrapper);
        log.info("CheckRepeatRequest (ID: {}): {}", testCheckRepeatId, checkResult != null ? "✅ 存在" : "❌ 不存在");
        
        log.info("【分库分表表】测试数据验证：");
        // 验证 IdentifyRequest 测试数据
        LambdaQueryWrapper<IdentifyRequest> identifyWrapper = new LambdaQueryWrapper<>();
        identifyWrapper.eq(IdentifyRequest::getResponseId, testIdentifyResponseId);
        IdentifyRequest identifyResult = identifyRequestService.getOne(identifyWrapper);
        log.info("IdentifyRequest (ID: {}): {}", testIdentifyResponseId, identifyResult != null ? "✅ 存在" : "❌ 不存在");
        
        log.info("==================================================");
        log.info("测试数据验证完成");
        log.info("注意：未分库分表表的测试数据应在 ds0 中存在但在 ds1 中不存在");
        log.info("注意：分库分表表的测试数据按分片规则存在对应数据源中");
    }

    /**
     * 快速测试方法
     * 只查询10次，用于快速验证配置是否正确
     */
    @Test
    public void quickTest() {
        log.info("开始快速测试（每个表10次查询）...");
        
        int quickTestCount = 10;
        
        // 测试数据配置（与主测试方法保持一致）
        long testAppealId = 1349260197004115968L;
        String testResponseGroupId = "3074866662265847808";
        String testCheckRepeatId = "2710406186108583938";
        String testIdentifyResponseId = "3702217176393187328";
        
        // 测试结果统计
        int appealNullCount = 0;
        int batchNullCount = 0;
        int checkNullCount = 0;
        int identifyNullCount = 0;
        
        log.info("快速测试配置：");
        log.info("【未分库分表表】- 受 default-data-source-name 配置影响：");
        log.info("- AppealRequest 测试ID: {}", testAppealId);
        log.info("- BatchIdentifyRequest 测试ID: {}", testResponseGroupId);
        log.info("- CheckRepeatRequest 测试ID: {}", testCheckRepeatId);
        log.info("【分库分表表】- 不受 default-data-source-name 配置影响：");
        log.info("- IdentifyRequest 测试ID: {}", testIdentifyResponseId);
        log.info("- 每个表查询次数: {}", quickTestCount);
        
        // 执行快速测试
        for (int i = 0; i < quickTestCount; i++) {
            // 测试 AppealRequest
            LambdaQueryWrapper<AppealRequest> appealWrapper = new LambdaQueryWrapper<>();
            appealWrapper.eq(AppealRequest::getAppealId, testAppealId);
            AppealRequest appealResult = appealRequestService.getOne(appealWrapper);
            if (appealResult == null) {
                appealNullCount++;
            }
            
            // 测试 BatchIdentifyRequest
            LambdaQueryWrapper<BatchIdentifyRequest> batchWrapper = new LambdaQueryWrapper<>();
            batchWrapper.eq(BatchIdentifyRequest::getResponseGroupId, testResponseGroupId);
            BatchIdentifyRequest batchResult = batchIdentifyRequestService.getOne(batchWrapper);
            if (batchResult == null) {
                batchNullCount++;
            }
            
            // 测试 CheckRepeatRequest
            LambdaQueryWrapper<CheckRepeatRequest> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(CheckRepeatRequest::getCheckRepeatId, testCheckRepeatId);
            CheckRepeatRequest checkResult = checkRepeatRequestService.getOne(checkWrapper);
            if (checkResult == null) {
                checkNullCount++;
            }
            
            // 测试 IdentifyRequest（分库分表）
            LambdaQueryWrapper<IdentifyRequest> identifyWrapper = new LambdaQueryWrapper<>();
            identifyWrapper.eq(IdentifyRequest::getResponseId, testIdentifyResponseId);
            IdentifyRequest identifyResult = identifyRequestService.getOne(identifyWrapper);
            if (identifyResult == null) {
                identifyNullCount++;
            }
        }
        
        // 输出快速测试结果
        log.info("==================================================");
        log.info("快速测试结果汇总");
        log.info("==================================================");
        
        log.info("【未分库分表表】结果：");
        log.info("AppealRequest: {}/{} 成功, {} null", 
                quickTestCount - appealNullCount, quickTestCount, appealNullCount);
        log.info("BatchIdentifyRequest: {}/{} 成功, {} null", 
                quickTestCount - batchNullCount, quickTestCount, batchNullCount);
        log.info("CheckRepeatRequest: {}/{} 成功, {} null", 
                quickTestCount - checkNullCount, quickTestCount, checkNullCount);
        
        log.info("【分库分表表】结果：");
        log.info("IdentifyRequest: {}/{} 成功, {} null", 
                quickTestCount - identifyNullCount, quickTestCount, identifyNullCount);
        
        int totalNullCount = appealNullCount + batchNullCount + checkNullCount;
        int totalQueries = quickTestCount * 3;
        
        log.info("==================================================");
        log.info("【未分库分表表】总体结果: {}/{} 成功, {} null", 
                totalQueries - totalNullCount, totalQueries, totalNullCount);
        
        if (totalNullCount == 0) {
            log.info("✅ 未分库分表表快速测试通过，数据源路由配置可能正确");
        } else {
            log.warn("⚠️ 未分库分表表快速测试失败，存在数据源路由问题");
            log.warn("建议检查 default-data-source-name 配置");
        }
        
        if (identifyNullCount == 0) {
            log.info("✅ 分库分表表快速测试通过，符合预期（不受 default-data-source-name 影响）");
        } else {
            log.warn("⚠️ 分库分表表快速测试失败，可能存在数据问题或分片配置问题");
        }
        
        log.info("建议运行完整测试进行最终确认");
        log.info("==================================================");
    }
}

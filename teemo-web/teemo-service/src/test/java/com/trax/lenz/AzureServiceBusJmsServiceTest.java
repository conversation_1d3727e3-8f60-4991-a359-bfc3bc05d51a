package com.trax.lenz;

import com.trax.lenz.service.message.PepsiAzureServiceBusJmsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest
@ActiveProfiles("local") // 可根据实际环境调整
public class AzureServiceBusJmsServiceTest {

    @Autowired
    private PepsiAzureServiceBusJmsService pepsiAzureServiceBusJmsService;

    @Test
    public void testSendMessage() {
        String testMsg = "测试消息 " + System.currentTimeMillis();
        boolean result = pepsiAzureServiceBusJmsService.sendMessage(testMsg);
        System.out.println("消息发送结果: " + result);
        assert result;
    }
} 
package com.trax.lenz;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件差异比较测试类
 * 比较1.txt和2.txt的差异，输出到3.txt
 */
public class FileDiffTest {
    
    public static void main(String[] args) {
        String file1Path = "D://retry/1.txt";
        String file2Path = "D://retry/2.txt";
        String outputPath = "D://retry/3.txt";
        
        try {
            // 读取两个文件
            Set<String> lines1 = readFile(file1Path);
            Set<String> lines2 = readFile(file2Path);
            
            // 比较差异
            FileDiffResult result = compareFiles(lines1, lines2);
            
            // 输出结果到文件
            writeResultToFile(result, outputPath);
            
            // 打印统计信息
            printStatistics(result);
            
        } catch (IOException e) {
            System.err.println("文件操作失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 读取文件内容到Set中
     */
    private static Set<String> readFile(String filePath) throws IOException {
        if (!Files.exists(Paths.get(filePath))) {
            System.out.println("文件不存在: " + filePath + "，将创建空文件");
            Files.createFile(Paths.get(filePath));
            return new HashSet<>();
        }
        
        return Files.lines(Paths.get(filePath))
                   .map(String::trim)
                   .filter(line -> !line.isEmpty())
                   .collect(Collectors.toSet());
    }
    
    /**
     * 比较两个文件的差异
     */
    private static FileDiffResult compareFiles(Set<String> lines1, Set<String> lines2) {
        FileDiffResult result = new FileDiffResult();
        
        // 只在1.txt中有的行
        result.onlyInFile1 = new HashSet<>(lines1);
        result.onlyInFile1.removeAll(lines2);
        
        // 只在2.txt中有的行
        result.onlyInFile2 = new HashSet<>(lines2);
        result.onlyInFile2.removeAll(lines1);
        
        // 两个文件都有的行
        result.commonLines = new HashSet<>(lines1);
        result.commonLines.retainAll(lines2);
        
        return result;
    }
    
    /**
     * 将结果写入文件
     */
    private static void writeResultToFile(FileDiffResult result, String outputPath) throws IOException {
        List<String> outputLines = new ArrayList<>();
        
        outputLines.add("=== 文件差异比较结果 ===");
        outputLines.add("");
        
        outputLines.add("【只在1.txt中有的行】");
        outputLines.add("数量: " + result.onlyInFile1.size());
        if (result.onlyInFile1.isEmpty()) {
            outputLines.add("无");
        } else {
            outputLines.addAll(result.onlyInFile1);
        }
        outputLines.add("");
        
        outputLines.add("【只在2.txt中有的行】");
        outputLines.add("数量: " + result.onlyInFile2.size());
        if (result.onlyInFile2.isEmpty()) {
            outputLines.add("无");
        } else {
            outputLines.addAll(result.onlyInFile2);
        }
        outputLines.add("");
        
        outputLines.add("【两个文件都有的行】");
        outputLines.add("数量: " + result.commonLines.size());
        if (result.commonLines.isEmpty()) {
            outputLines.add("无");
        } else {
            outputLines.addAll(result.commonLines);
        }
        
        Files.write(Paths.get(outputPath), outputLines);
        System.out.println("结果已写入文件: " + outputPath);
    }
    
    /**
     * 打印统计信息
     */
    private static void printStatistics(FileDiffResult result) {
        System.out.println("\n=== 统计信息 ===");
        System.out.println("只在1.txt中有的行数: " + result.onlyInFile1.size());
        System.out.println("只在2.txt中有的行数: " + result.onlyInFile2.size());
        System.out.println("两个文件都有的行数: " + result.commonLines.size());
        System.out.println("总差异行数: " + (result.onlyInFile1.size() + result.onlyInFile2.size()));
    }
    
    /**
     * 文件差异结果类
     */
    static class FileDiffResult {
        Set<String> onlyInFile1;    // 只在1.txt中有的行
        Set<String> onlyInFile2;    // 只在2.txt中有的行
        Set<String> commonLines;    // 两个文件都有的行
    }
}

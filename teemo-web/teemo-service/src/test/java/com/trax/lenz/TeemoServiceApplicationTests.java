package com.trax.lenz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.entity.IdentifyRequest;
import com.trax.lenz.service.IdentifyRequestService;
import com.trax.lenz.service.OssService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.nio.charset.StandardCharsets;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("local")
public class TeemoServiceApplicationTests {

    @Autowired
    private OssService ossService;

    @Autowired
    private IdentifyRequestService identifyRequestService;

    @Test
    public void test() {
        String aa = "12313213";
        ossService.uploadV2("rule1/1/2/3/xxx.json", aa);
    }

    @Test
    public void test2() {
        LambdaQueryWrapper<IdentifyRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IdentifyRequest::getResponseId, "3257927273753870342");
        IdentifyRequest identifyRequest = identifyRequestService.getOne(wrapper);
        log.info("-------------{}", JsonUtil.toJsonString(identifyRequest));

        LambdaQueryWrapper<IdentifyRequest> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(IdentifyRequest::getResponseId, "3709125888986841088");
        IdentifyRequest identifyRequest2 = identifyRequestService.getOne(wrapper2);
        log.info("-------------{}", JsonUtil.toJsonString(identifyRequest2));

        LambdaQueryWrapper<IdentifyRequest> wrapper3 = new LambdaQueryWrapper<>();
        wrapper3.eq(IdentifyRequest::getResponseId, "3791247828350140416");
        IdentifyRequest identifyRequest3 = identifyRequestService.getOne(wrapper3);
        log.info("-------------{}", JsonUtil.toJsonString(identifyRequest3));
    }
}

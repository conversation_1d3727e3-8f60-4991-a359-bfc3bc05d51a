# Tomcat
server:
  port: 18802
# Spring
spring:
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  main:
    allow-bean-definition-overriding: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  cache:
    redis:
      key-prefix: teemo
  application:
    # 应用名称
    name: teemo-service
  profiles:
    # 环境配置
    active: local
  cloud:
    sentinel:
      transport:
        port: 11100
      log:
        dir: ${logging.file.path}/${spring.application.name}/sentinel
    nacos:
      username: local
      password: ppz#2021@local
      # 服务注册地址
      server-addr: txnacos.langjtech.com:80
      discovery:
        namespace: ${spring.profiles.active}
        group: Tope
        metadata:
          preserved.heart.beat.interval: 1000 #心跳间隔。时间单位:秒。心跳间隔
          preserved.heart.beat.timeout: 3000 #心跳暂停。时间单位:秒。 即服务端6秒收不到客户端心跳，会将该客户端注册的实例设为不健康：
          preserved.ip.delete.timeout: 6000 #Ip删除超时。时间单位:秒。即服务端9秒收不到客户端心跳，会将该客户端注册的实例删除：
      config:
        namespace: ${spring.profiles.active}
        group: Tope
        refresh-enabled: true
        prefix: ${spring.application.name}
        # 配置文件格式
        file-extension: yml
mybatis-plus:
  mapper-locations: classpath:mybatis/mappers/**/*.xml
  type-aliases-package: com.trax.lenz.teemo.entity
  global-config:
    db-config:
      # 逻辑删除
      logic-delete-value: 1
      logic-not-delete-value: 0
# 雪花算法配置数据中心和机器编号，不同机器组合不能重复
snowflake:
  datacenterId: 1
  machineId: 1
logging:
  file:
    path: ./data/logs
    name: ${logging.file.path}/${spring.application.name}/info.log
# 暴露监控端点
management:
  health:
    db:
      enabled: false
  endpoints:
    web:
      exposure:
        include: "*"
        exclude: env
    jmx:
      exposure:
        include: "*"
  endpoint:
    prometheus:
      enabled: true
    logfile:
      enabled: true
      external-file: ${logging.file.name}
    health:
      enabled: true
      show-details: always
    info:
      enabled: true
    shutdown:
      enabled: false
    httptrace:
      enabled: true
    metrics:
      enabled: true
  metrics:
    tags:
      application: ${spring.application.name}

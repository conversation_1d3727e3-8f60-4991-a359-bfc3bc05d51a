<?xml version="1.0" encoding="UTF-8"?>

<configuration debug="false" scan="true" scanPeriod="30 seconds" packagingData="true">
    <contextName>nacos</contextName>
    <springProperty scope="context" name="LOG_PATH" source="logging.file.path"></springProperty>
    <springProperty scope="context" name="APP_NAME" source="spring.application.name"></springProperty>
    <property name="LOG_PATH" value="${LOG_PATH:-./data/logs}"/>
    <property name="APP_NAME" value="${APP_NAME:-tmp}"/>

    <appender name="CONFIG_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <Pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}][%level][%r][%X{traceId},%X{spanId}][%thread][%-40.40logger][%M\(%line\)]%msg%n</Pattern>
        </encoder>

        <file>${LOG_PATH}/${APP_NAME}/nacos/config.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_NAME}/nacos/config.log.%i</fileNamePattern>
            <maxIndex>${JM.LOG.RETAIN.COUNT:-7}</maxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>${JM.LOG.FILE.SIZE:-10MB}</maxFileSize>
        </triggeringPolicy>

    </appender>

    <appender name="NAMING_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <Pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}][%level][%r][%X{traceId},%X{spanId}][%thread][%-40.40logger][%M\(%line\)]%msg%n</Pattern>
        </encoder>

        <file>${LOG_PATH}/${APP_NAME}/nacos/naming.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_NAME}/nacos/naming.log.%i</fileNamePattern>
            <maxIndex>${JM.LOG.RETAIN.COUNT:-7}</maxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>${JM.LOG.FILE.SIZE:-10MB}</maxFileSize>
        </triggeringPolicy>

    </appender>


    <logger name="com.alibaba.nacos.client" level="${com.alibaba.nacos.config.log.level:-info}"
            additivity="false">
        <appender-ref ref="CONFIG_LOG_FILE"/>
    </logger>

    <logger name="com.alibaba.nacos.client.config" level="${com.alibaba.nacos.config.log.level:-info}"
            additivity="false">
        <appender-ref ref="CONFIG_LOG_FILE"/>
    </logger>

    <logger name="com.alibaba.nacos.client.naming" level="${com.alibaba.nacos.naming.log.level:-info}"
            additivity="false">
        <appender-ref ref="NAMING_LOG_FILE"/>
    </logger>

</configuration>

package com.trax.lenz.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.trax.lenz.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName test2Service
 * @Description:
 * <AUTHOR>
 * @Date 2024-11-12-15:11
 **/
@Service
@Slf4j
public class test2Service {

    private static final RestTemplate restTemplate = new RestTemplate();

    public static void main(String[] args) {
        // 获取文件名称
        File file = new File("/Users/<USER>/Pictures/20240411-112623.jpeg");

        String callbackUrl = "https://storage-qa.pg.com.cn/v2/files";
        try {
//            MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
            Long timeMilis = System.currentTimeMillis();

            Map<String, String> signMap = new HashMap<>();
            signMap.put("subscriptionKey", "ghr-golden-hand-psd-100641");
            signMap.put("public", "true");
            signMap.put("timestamp", String.valueOf(timeMilis));
            signMap.put("subscriptionSecret", "f0566352135c903ebca930deac1c1ae480f37317");


            Map<String, Object> param = Maps.newHashMap();
            param.put("name", "20240411-112623.jpeg");
            param.put("signature", signature(signMap));
            param.put("subscriptionKey", "ghr-golden-hand-psd-100641");
            param.put("public", "true");
            param.put("timestamp", String.valueOf(timeMilis));

            String contentType = MediaType.IMAGE_JPEG_VALUE;
            String s = fileUploadRequest(param, file, callbackUrl, contentType);
            log.info("File upload result === " + s);
        } catch (Exception e) {
            log.info("文件上传失败，正在重试=======");
            log.error("file upload error==", e);
            throw new BusinessException("7", "文件上传异常,请重试");
        }


    }

    public static String fileUploadRequest(Map<String, Object> paramMap, File file, String url, String contentType) {
        log.info("File upload start ===");
        log.info("File size === " + file.length());

        try {
            // 将文件内容转换为 ByteArrayResource
            byte[] fileBytes = Files.readAllBytes(file.toPath());
            ByteArrayResource fileAsResource = new ByteArrayResource(fileBytes) {
                @Override
                public String getFilename() {
                    return file.getName();
                }
            };

            HttpHeaders headers = new HttpHeaders();

            HttpHeaders fileHeaders = new HttpHeaders();
            fileHeaders.setContentType(MediaType.parseMediaType(contentType));
            fileHeaders.setContentDispositionFormData("file", file.getName());

            // 创建 HttpEntity 用于上传文件部分
            HttpEntity<ByteArrayResource> fileEntity = new HttpEntity<>(fileAsResource, fileHeaders);
            MultiValueMap<String, Object> hashMap = new LinkedMultiValueMap<>();
            for (String key : paramMap.keySet()) {
                hashMap.add(key, paramMap.get(key));
            }
            hashMap.add("file", fileEntity);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(hashMap, headers);

            // 发起 POST 请求
            String response = restTemplate.postForObject(url, requestEntity, String.class);
            log.info("File upload end ===");
            log.info("File upload result === " + response);

            return response;

        } catch (IOException e) {
            log.error("File read error:", e);
            throw new BusinessException("46", "文件读取异常");
        } catch (Exception e) {
            log.error("调用远程服务异常: url: " + url, e);
            throw new BusinessException("46", "调用接口异常");
        }
    }

    // 自定义的异常
    static class BusinessException extends RuntimeException {
        public BusinessException(String code, String message) {
            super(message);
            log.error("BusinessException: code={}, message={}", code, message);
        }
    }

    public static String signature(Map<String, String> paramsMap) {
        try {
            List<String> list = new ArrayList<>();
            for (Map.Entry<String, String> element : paramsMap.entrySet()) {
                list.add(concatParamStr(element.getKey(), element.getValue()));
            }
            StringBuilder sb = new StringBuilder();
            Collections.sort(list);
            for (String sigParam : list) {
                sb.append(sigParam);
            }
            return DigestUtils.sha1Hex(sb.toString().getBytes(StandardCharsets.UTF_8.name()));
        } catch (Exception e) {

        }
        return "";
    }

    public static String concatParamStr(String key, String value) {
        return key.concat("=").concat(value);
    }
}

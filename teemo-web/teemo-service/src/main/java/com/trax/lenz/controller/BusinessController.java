package com.trax.lenz.controller;

import com.trax.lenz.api.dto.response.GetAiResultLatestResp;
import com.trax.lenz.api.dto.response.GetStitchImageSourceImageResp;
import com.trax.lenz.api.dto.response.ResponseData;
import com.trax.lenz.common.annotation.ResponseResult;
import com.trax.lenz.common.core.domain.R;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.dto.request.IdentifyParameterReq;
import com.trax.lenz.dto.request.submit.PgIdentifySubmitReq;
import com.trax.lenz.dto.response.CustomerImageRelationResp;
import com.trax.lenz.dto.response.GetImageSkuDetailResultResp;
import com.trax.lenz.dto.response.GetImageSkuResultResp;
import com.trax.lenz.dto.response.IdentifyParameterResp;
import com.trax.lenz.dto.response.PgIdentifySubmitResp;
import com.trax.lenz.dto.response.apiRepeat.CallbackCustomerRepeatResultResp;
import com.trax.lenz.dto.response.apiRepeat.CallbackCustomerRepeatResultRespV3;
import com.trax.lenz.gabriel.dto.request.AddAppealReq;
import com.trax.lenz.service.AppealService;
import com.trax.lenz.service.CallbackService;
import com.trax.lenz.service.CheckRepeatService;
import com.trax.lenz.service.IdentifyService;
import com.trax.lenz.service.RedisTemplateService;
import com.trax.lenz.service.remote.ApiService;
import com.trax.lenz.service.remote.GrpcClientService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 业务入口Controller
 *
 * <AUTHOR>
 * @date 2022-09-09 17:03:48
 */
@Slf4j
@RestController
@RequestMapping("/business")
public class BusinessController {

    private final CallbackService callbackService;

    private final IdentifyService identifyService;

    private final AppealService appealService;

    private final RedisTemplateService redisService;

    private final ApiService apiService;

    private final CheckRepeatService checkRepeatService;

    private final GrpcClientService grpcClientService;

    public BusinessController(CallbackService callbackService, IdentifyService identifyService, AppealService appealService, RedisTemplateService redisService, ApiService apiService, CheckRepeatService checkRepeatService, GrpcClientService grpcClientService) {
        this.callbackService = callbackService;
        this.identifyService = identifyService;
        this.appealService = appealService;
        this.redisService = redisService;
        this.apiService = apiService;
        this.checkRepeatService = checkRepeatService;
        this.grpcClientService = grpcClientService;
    }

    @RequestMapping(value = "/hello", method = RequestMethod.GET)
    @ApiOperation(value = "测试")
    public String hello() {
        return "hello world!";
    }

    @RequestMapping(value = "/test", method = RequestMethod.GET)
    public void test() {
        redisService.test();
    }

    @RequestMapping(value = "/getImageSku", method = RequestMethod.GET)
    @ApiOperation(value = "测试获取单图商品")
    public GetStitchImageSourceImageResp getImageSku(@RequestParam("responseId") String responseId, @RequestParam("groupNo") Long groupNo) {
        return apiService.getStitchImageSourceImageList(responseId, groupNo);
    }

    @RequestMapping(value = "/getImageSkuDetailResult", method = RequestMethod.GET)
    @ApiOperation(value = "测试获取单图商品")
    public GetImageSkuDetailResultResp getImageSkuDetailResult(@RequestParam("responseId") String responseId) {
        return identifyService.getImageSkuDetailResult(responseId);
    }

    @RequestMapping(value = "/getImageSkuDetailResultV2", method = RequestMethod.GET)
    @ApiOperation(value = "测试获取单图商品V2")
    public GetImageSkuResultResp getImageSkuDetailResultV2(@RequestParam("responseId") String responseId) {
        return identifyService.getImageSkuResult(responseId);
    }

    @RequestMapping(value = "/pgSubmit", method = RequestMethod.POST)
    @ApiOperation(value = "测试宝洁混合模型")
    public PgIdentifySubmitResp pgSubmit(@RequestBody PgIdentifySubmitReq req) {
        return grpcClientService.pgSubmit(String.valueOf(req));
    }

    @RequestMapping(value = "/callback", method = RequestMethod.POST)
    @ApiOperation(value = "回调")
    public ResponseData callback(@RequestBody GetAiResultLatestResp resp) {
        try {
            callbackService.resultCallback(resp);
            return ResponseData.success();
        } catch (Exception e) {
            log.error("回调异常!", e);
            return ResponseData.failure();
        }
    }

    @RequestMapping(value = "/getCustomerInfo", method = RequestMethod.POST)
    @ApiOperation(value = "查询答卷提交流水信息")
    public R getCustomerInfo(@RequestBody IdentifyParameterReq req) {
        try {
            IdentifyParameterResp customerInfo = identifyService.getSubmitRequestByCustomerInfo(req);
            return R.ok().data(customerInfo);
        } catch (Exception e) {
            log.error("查询答卷提交流水信息失败!,{}, {}", e, e.getMessage());
        }
        return R.fail();
    }

    @RequestMapping(value = "/getImageMap", method = RequestMethod.GET)
    @ApiOperation(value = "查询图片Map")
    public R getImageMap(@RequestParam("responseId") String responseId) {
        try {
            Map<String, CustomerImageRelationResp> mappingMap = identifyService.getImageMap(responseId);
            log.info("getImageMap={}", JsonUtil.toJsonString(mappingMap));
            return R.ok().data(mappingMap);
        } catch (Exception e) {
            log.error("查询图片Map失败!", e);
        }
        return R.fail();
    }

    @RequestMapping(value = "/appeal/callback", method = RequestMethod.POST)
    @ApiOperation(value = "申诉回调")
    public R callback(@RequestBody AddAppealReq req) {
        try {
            appealService.callback(req);
            return R.ok();
        } catch (Exception e) {
            log.error("申诉回调异常!", e);
        }
        return R.fail();
    }

    @ResponseResult
    @RequestMapping(value = "/checkRepeat/callback", method = RequestMethod.POST)
    @ApiOperation(value = "查重回调")
    public void checkRepeat(@RequestBody CallbackCustomerRepeatResultResp req) {
        checkRepeatService.callback(req);
    }

    @ResponseResult
    @RequestMapping(value = "/checkRepeat/callbackV3", method = RequestMethod.POST)
    @ApiOperation(value = "查重回调V3")
    public void checkRepeatV3(@RequestBody CallbackCustomerRepeatResultRespV3 req) {
        checkRepeatService.callbackV3(req);
    }

    /**
     * 查重回调V4版本
     * 此版本支持了新子能力表回调支持
     *
     * @param req
     */
    @ResponseResult
    @RequestMapping(value = "/checkRepeat/callbackV4", method = RequestMethod.POST)
    @ApiOperation(value = "查重回调V4")
    public void checkRepeatV4(@RequestBody CallbackCustomerRepeatResultRespV3 req) {
        checkRepeatService.callbackV4(req);
    }
}

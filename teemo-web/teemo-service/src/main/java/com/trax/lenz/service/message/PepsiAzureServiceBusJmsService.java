package com.trax.lenz.service.message;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Service;

import javax.jms.MessageProducer;
import javax.jms.TextMessage;

/**
 * 独立的 Azure Service Bus JMS 消息发送服务
 *
 * <AUTHOR>
 */
@Service
@RefreshScope
@Slf4j
public class PepsiAzureServiceBusJmsService {

    private final JmsTemplate jmsTemplate;

    @Value("${spring.jms.servicebus.queue-name}")
    private String queueName;

    @Autowired
    public PepsiAzureServiceBusJmsService(JmsTemplate jmsTemplate) {
        this.jmsTemplate = jmsTemplate;
    }

    /**
     * 发送消息到配置文件指定队列，带重试机制
     *
     * @param message 消息内容
     * @return true=发送成功，false=发送失败
     */
    public boolean sendMessage(String message) {
        int maxRetry = 3;
        for (int i = 1; i <= maxRetry; i++) {
            try {
                jmsTemplate.setDeliveryPersistent(true); // 持久化
                String messageId = jmsTemplate.execute(session -> {
                    MessageProducer producer = session.createProducer(session.createQueue(queueName));
                    TextMessage textMessage = session.createTextMessage(message);
                    producer.send(textMessage);
                    return textMessage.getJMSMessageID(); // 发送后立即获取消息ID
                });

                log.info("消息发送成功，队列：{}，内容：{}，消息ID：{}", queueName, message, messageId);
                return true;
            } catch (Exception e) {
                log.error("第{}次发送失败，队列：{}，内容：{}，异常：{}", i, queueName, message, e.getMessage(), e);
                try {
                    Thread.sleep(500L * i);
                } catch (InterruptedException ignored) {
                }
            }
        }
        log.error("消息最终发送失败，队列：{}，内容：{}", queueName, message);
        return false;
    }
} 
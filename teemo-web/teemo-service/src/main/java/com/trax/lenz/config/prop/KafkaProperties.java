package com.trax.lenz.config.prop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @ClassName KafkaProperties
 * @Description:
 * <AUTHOR>
 * @Date 2024-10-24-17:50
 **/
@Data
@ConfigurationProperties(prefix = "spring.kafka")
@Component
public class KafkaProperties {

    private Boolean enabled;

    private Boolean extranetEnabled;

    private String username;

    private String password;

    private String servers;

    private Producer producer;

    private Consumer consumer;

    private Provider provider;

    @Data
    public static class Provider {
        private boolean enabledAutoCreate;
        private String name;
        private String accessKeyId;
        private String accessKeySecret;
        private String endpoint;
        private String instanceId;
        private String regionId;
    }

    @Data
    public static class Producer {

        private String clientId;

        private Integer retries;

        private String acks;

        private Integer batchSize;

        private Integer bufferMemory;

        private String keySerializer;

        private String valueSerializer;

        private Integer lingerMs;

        private Map<String, String> topics;

    }

    @Data
    public static class Consumer {

        private String groupId;

        private Boolean enableAutoCommit;

        private Integer maxPollRecords;

        private String keyDeserializer;

        private String valueDeserializer;

        private Map<String, String> topics;
    }
}

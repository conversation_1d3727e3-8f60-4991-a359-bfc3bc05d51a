package com.trax.lenz.controller.submit;

import com.trax.lenz.common.core.domain.R;
import com.trax.lenz.dto.request.submit.SnowBeerBatchSubmitReq;
import com.trax.lenz.service.submit.SnowBeerService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * @ClassName SnowbeerController
 * @Description: 雪花提交接口
 * <AUTHOR>
 * @Date 2024-03-27-14:11
 **/
@Slf4j
@RestController
@RequestMapping("/trax/snowbeer")
public class SnowBeerController {

    private final SnowBeerService snowBeerService;

    public SnowBeerController(SnowBeerService snowBeerService) {
        this.snowBeerService = snowBeerService;
    }

    @RequestMapping(value = "/batchSubmit", method = RequestMethod.POST)
    @ApiOperation(value = "雪花项目批量提交任务导入")
    public R batchSubmit(SnowBeerBatchSubmitReq req, @RequestParam(value = "file") MultipartFile file) {
        snowBeerService.batchSubmit(req, file);
        return R.ok();
    }

    @RequestMapping(value = "/compensationExecution", method = RequestMethod.POST)
    @ApiOperation(value = "补偿执行")
    public R compensationExecution(@RequestBody SnowBeerBatchSubmitReq req) {
        snowBeerService.compensationExecution(req);
        return R.ok();
    }

}

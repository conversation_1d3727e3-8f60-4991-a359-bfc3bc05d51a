package com.trax.lenz.controller;

import com.trax.lenz.enums.SnowCode;
import com.trax.lenz.common.core.id.SnowFlakeFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Controller
public class IndexController {

  @Autowired
  private SnowFlakeFactory snowFlakeFactory;

  @GetMapping
  public String index() {
    return "redirect:/actuator/health";
  }

  @GetMapping("/batchCreateID")
  @ResponseBody
  public Set batchCreateID() throws ExecutionException, InterruptedException {
    int count = 2000;
    System.out.println("开始生成id......");
    ExecutorService executor = new ThreadPoolExecutor(5,5,60, TimeUnit.MINUTES, new ArrayBlockingQueue<Runnable>(1));
    List countList = new ArrayList();
    //测试生成20w个id
    for (int i = 0; i < 200000; i++) {
      countList.add(i);
    }
    //使用set测试是否有重复，结果没有任何重复
    Set list = Collections.synchronizedSet(new HashSet<>());
    System.out.println("开始时间" + LocalDateTime.now());
    countList.parallelStream().forEach((i) -> {
      Future<String> futureTask = executor.submit(() -> {
        return snowFlakeFactory.nextIdStr();
      });
      String id = null;
      try {
        id = futureTask.get();
      } catch (Exception e2) {
        e2.printStackTrace();
      }
      list.add(id);
    });
    System.out.println("结束时间" + LocalDateTime.now());
    System.out.println(list.size());
    return list;
  }
}

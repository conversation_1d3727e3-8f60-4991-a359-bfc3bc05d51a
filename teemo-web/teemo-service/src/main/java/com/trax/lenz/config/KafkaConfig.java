package com.trax.lenz.config;

import com.trax.lenz.common.constants.AppConstants;
import com.trax.lenz.config.prop.CustomProperties;
import com.trax.lenz.config.prop.KafkaProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.config.SslConfigs;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.Properties;

/**
 * @ClassName KafkaConfig
 * @Description: kafka配置
 * <AUTHOR>
 * @Date 2024-10-23-19:32
 **/
@Slf4j
@Configuration
@ConditionalOnProperty(name = "spring.kafka.enabled", havingValue = AppConstants.TRUE_STR)
@EnableConfigurationProperties({KafkaProperties.class, CustomProperties.class})
public class KafkaConfig {

    public final CustomProperties customProperties;

    public KafkaConfig(CustomProperties customProperties) {
        this.customProperties = customProperties;
    }

    @Bean
    public Properties producerConfigs(KafkaProperties kafkaProperties) {
        Properties props = new Properties();
        // 集群的服务器地址
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaProperties.getServers());
        // 消息缓冲区内存大小，缓冲区如果满了，则会阻塞，生产者报错 默认值为：33554432合计为32M
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, kafkaProperties.getProducer().getBufferMemory());
        // 生产者重试次数
        props.put(ProducerConfig.RETRIES_CONFIG, kafkaProperties.getProducer().getRetries());
        // 应答级别 0、立即返回 1、主副本应答 -1、全部副本应答
        props.put(ProducerConfig.ACKS_CONFIG, kafkaProperties.getProducer().getAcks());
        // kafka以批量为单位进行提交，通俗讲就是攒够一定数量的消息后，一起提交，下面两个参数满足任意一个时都会触发提交动作， 默认值：16384、0
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, kafkaProperties.getProducer().getBatchSize());
        props.put(ProducerConfig.LINGER_MS_CONFIG, kafkaProperties.getProducer().getLingerMs());
        // key 和 value 的序列化
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, kafkaProperties.getProducer().getKeySerializer());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, kafkaProperties.getProducer().getValueSerializer());
        // 客户端id
        props.put(ProducerConfig.CLIENT_ID_CONFIG, kafkaProperties.getProducer().getClientId());
        if (kafkaProperties.getExtranetEnabled()) {
            log.info("kafka公网访问，增加Sasl配置！");
            this.configureSasl(kafkaProperties, props);
        }
        return props;
    }

    @Bean
    public Properties producerConfigsByte(KafkaProperties kafkaProperties) {
        Properties props = new Properties();
        // 集群的服务器地址
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaProperties.getServers());
        // 消息缓冲区内存大小，缓冲区如果满了，则会阻塞，生产者报错 默认值为：33554432合计为32M
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, kafkaProperties.getProducer().getBufferMemory());
        // 生产者重试次数
        props.put(ProducerConfig.RETRIES_CONFIG, kafkaProperties.getProducer().getRetries());
        // 应答级别 0、立即返回 1、主副本应答 -1、全部副本应答
        props.put(ProducerConfig.ACKS_CONFIG, kafkaProperties.getProducer().getAcks());
        // kafka以批量为单位进行提交，通俗讲就是攒够一定数量的消息后，一起提交，下面两个参数满足任意一个时都会触发提交动作， 默认值：16384、0
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, kafkaProperties.getProducer().getBatchSize());
        props.put(ProducerConfig.LINGER_MS_CONFIG, kafkaProperties.getProducer().getLingerMs());
        // key 和 value 的序列化
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, kafkaProperties.getProducer().getKeySerializer());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.ByteArraySerializer");
        // 客户端id
        props.put(ProducerConfig.CLIENT_ID_CONFIG, kafkaProperties.getProducer().getClientId());
        if (kafkaProperties.getExtranetEnabled()) {
            log.info("kafka公网访问，增加Sasl配置！");
            this.configureSasl(kafkaProperties, props);
        }
        return props;
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate(Properties producerConfigs) {
        ProducerFactory<String, String> producerFactory = new DefaultKafkaProducerFactory(producerConfigs);
        return new KafkaTemplate<>(producerFactory);
    }

    @Bean
    public Properties consumerConfigs(KafkaProperties kafkaProperties) {
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaProperties.getServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, kafkaProperties.getConsumer().getGroupId());

        //设置单次拉取的量，走公网访问时，该参数会有较大影响。
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 32000);
        props.put(ConsumerConfig.FETCH_MAX_BYTES_CONFIG, 32000);

        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaProperties.getConsumer().getMaxPollRecords());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, kafkaProperties.getConsumer().getEnableAutoCommit());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, kafkaProperties.getConsumer().getKeyDeserializer());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, kafkaProperties.getConsumer().getValueDeserializer());
        //15分钟不响应会被踢掉，触发rebalance
        props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 900000);
        if (kafkaProperties.getExtranetEnabled()) {
            log.info("kafka公网访问，增加Sasl配置！");
            this.configureSasl(kafkaProperties, props);
        }
        return props;
    }

    private void configureSasl(KafkaProperties kafkaProperties, Properties props) {
        if (AppConstants.CLOUD_PROVIDER_ALI.equalsIgnoreCase(kafkaProperties.getProvider().getName())) {
            if (null == System.getProperty("java.security.auth.login.config")) {
                //这个路径必须是一个文件系统可读的路径，不能被打包到JAR中。
                System.setProperty("java.security.auth.login.config", customProperties.getJavaSecurityAuthLoginConfig());
            }
            props.put(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG, customProperties.getSslTruststoreLocation());
            //根证书存储的密码，保持不变。
            props.put(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG, "KafkaOnsClient");
            //接入协议，目前支持使用SASL_SSL协议接入。
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            //SASL鉴权方式，保持不变。
            props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            //Hostname校验改成空。
            props.put(SslConfigs.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG, "");
        } else {
            log.info("kafka公网访问，增加Sasl配置！");
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
            props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            // 用户名密码，注：用户名是需要拼接，并非管控台的用户名：instanceId#username
            props.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"" + kafkaProperties.getUsername() + "\" password=\"" + kafkaProperties.getPassword() + "\";");
        }
    }
}

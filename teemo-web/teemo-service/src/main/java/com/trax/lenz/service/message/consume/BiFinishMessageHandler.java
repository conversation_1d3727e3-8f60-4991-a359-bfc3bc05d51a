package com.trax.lenz.service.message.consume;

import com.fasterxml.jackson.core.type.TypeReference;
import com.trax.lenz.common.constants.AppConstants;
import com.trax.lenz.common.utils.CglibCopyBeanUtil;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.dto.bi.BiDataFinish;
import com.trax.lenz.entity.*;
import com.trax.lenz.enums.IdentifyStatus;
import com.trax.lenz.enums.SubmitType;
import com.trax.lenz.service.FunctionService;
import com.trax.lenz.service.impl.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @ClassName BiFinishMessageHandler
 * @Description: BI处理完成消息消费
 * <AUTHOR>
 * @Date 2024-10-24-10:51
 **/
@Slf4j
@RefreshScope
@Component
public class BiFinishMessageHandler implements MessageHandler {

    @Value("${spring.kafka.consumer.topics.biMessageFinishSync:TOPIC-BI-TO-TEEMO-FINISH-DEV}")
    private String biFinishTopic;

    private final TaskConfigServiceImpl taskConfigService;

    public final FunctionService functionService;

    public final BatchIdentifyRequestServiceImpl batchIdentifyRequestService;

    public final PgBatchAppealRequestServiceImpl batchAppealRequestService;

    public final BatchIdentifyRequestNonRealtimeServiceImpl batchIdentifyRequestNonRealtimeService;

    public final PgBatchAppealRequestNonRealtimeServiceImpl batchAppealRequestNonRealtimeService;

    public BiFinishMessageHandler(TaskConfigServiceImpl taskConfigService, FunctionService functionService, BatchIdentifyRequestServiceImpl batchIdentifyRequestService, PgBatchAppealRequestServiceImpl batchAppealRequestService, BatchIdentifyRequestNonRealtimeServiceImpl batchIdentifyRequestNonRealtimeService, PgBatchAppealRequestNonRealtimeServiceImpl batchAppealRequestNonRealtimeService) {
        this.taskConfigService = taskConfigService;
        this.functionService = functionService;
        this.batchIdentifyRequestService = batchIdentifyRequestService;
        this.batchAppealRequestService = batchAppealRequestService;
        this.batchIdentifyRequestNonRealtimeService = batchIdentifyRequestNonRealtimeService;
        this.batchAppealRequestNonRealtimeService = batchAppealRequestNonRealtimeService;
    }

    @Override
    public boolean support(String topic) {
        return biFinishTopic.equalsIgnoreCase(topic);
    }

    @Override
    public void handle(String message) {
        try {
            log.info("接收到【BI批量计算处理完成】消息! message={}", message);
            BiDataFinish biDataFinish = JsonUtil.parseObject(message, new TypeReference<BiDataFinish>() {
            });
            String taskId = biDataFinish.getTaskId();
            String responseGroupId = biDataFinish.getResponseGroupId();
            String batchAppealId = biDataFinish.getBatchAppealId();
            String realtimeFlag = biDataFinish.getBatchAppealId();
            // 保存消息流水
            BatchIdentifyRequest batchIdentifyRequest = new BatchIdentifyRequest();
            batchIdentifyRequest.setResponseGroupId(responseGroupId);
            Integer resultStatus = IdentifyStatus.RECOGNITION_FINISH.getCode();

            // 判断是否计算出错
            if (StringUtils.isNotBlank(biDataFinish.getMessageBody())) {
                try {
                    // 直接解析messageBody为JSON数组
                    List<Map<String, Object>> messageBodyList = JsonUtil.parseObject(biDataFinish.getMessageBody(), 
                            new TypeReference<List<Map<String, Object>>>() {});
                    
                    // 检查是否有任何一个status为0
                    boolean hasError = false;
                    for (Map<String, Object> item : messageBodyList) {
                        // 检查主项目status
                        if (item.containsKey("status") && Integer.valueOf(0).equals(item.get("status"))) {
                            hasError = true;
                            break;
                        }
                        
                        // 检查子KPI的status
                        if (item.containsKey("sub_kpi_result")) {
                            List<Map<String, Object>> subKpiList = (List<Map<String, Object>>) item.get("sub_kpi_result");
                            for (Map<String, Object> subKpi : subKpiList) {
                                if (subKpi.containsKey("status") && Integer.valueOf(0).equals(subKpi.get("status"))) {
                                    hasError = true;
                                    break;
                                }
                            }
                            if (hasError) break;
                        }
                    }
                    
                    if (hasError) {
                        resultStatus = IdentifyStatus.BI_ERROR.getCode();
                    } else {
                        resultStatus = IdentifyStatus.RECOGNITION_FINISH.getCode();
                    }
                } catch (Exception e) {
                    log.error("解析BI计算结果出错 message={}, error message={}", message, e);
                    resultStatus = IdentifyStatus.BI_ERROR.getCode();
                }
            }

            batchIdentifyRequest.setBiRespContent(JsonUtil.toJsonString(biDataFinish));
            batchIdentifyRequest.setStatus(resultStatus);
            batchIdentifyRequest.setIdentifyFinishTime(new Date());
            TaskConfig taskConfig = null;
            Map<String, Object> params = new HashMap<>();
            if (StringUtils.isNotBlank(batchAppealId)){
                //非实时
                if (StringUtils.isNotBlank(realtimeFlag) && realtimeFlag.equals(AppConstants.GSK_SUCCESS_CODE)){
                    PgBatchAppealRequestNonRealtime batchAppealRequestNonRealtime = new PgBatchAppealRequestNonRealtime();
                    batchAppealRequestNonRealtime.setBiRespContent(JsonUtil.toJsonString(biDataFinish));
                    batchAppealRequestNonRealtime.setStatus(IdentifyStatus.RECOGNITION_FINISH.getCode());
                    batchAppealRequestNonRealtimeService.update(batchAppealId, batchAppealRequestNonRealtime);
                    params.put("realtimeFlag", "0");
                }else {
                    PgBatchAppealRequest batchAppealRequest = new PgBatchAppealRequest();
                    batchAppealRequest.setBiRespContent(JsonUtil.toJsonString(biDataFinish));
                    batchAppealRequest.setStatus(IdentifyStatus.RECOGNITION_FINISH.getCode());
                    batchAppealRequestService.update(batchAppealId, batchAppealRequest);
                    params.put("realtimeFlag", "1");
                }
                taskConfig = taskConfigService.getOne(taskId, SubmitType.BATCH_APPEAL.getCode());
                MDC.put(AppConstants.HEADER_TRACE_ID, batchAppealId);
                params.put("appealId", batchAppealId);

            }else {
                //非实时
                if (StringUtils.isNotBlank(realtimeFlag) && realtimeFlag.equals(AppConstants.GSK_SUCCESS_CODE)){
                    BatchIdentifyRequestNonRealtime batchIdentifyRequestNonRealtime = new BatchIdentifyRequestNonRealtime();
                    CglibCopyBeanUtil.basicCopyBean(batchIdentifyRequest, batchIdentifyRequestNonRealtime);
                    batchIdentifyRequestNonRealtimeService.update(responseGroupId, batchIdentifyRequestNonRealtime);
                    params.put("realtimeFlag", "0");
                }else {
                    batchIdentifyRequestService.update(responseGroupId, batchIdentifyRequest);
                    params.put("realtimeFlag", "1");
                }
                taskConfig = taskConfigService.getOne(taskId, SubmitType.BATCH_CALC.getCode());
                MDC.put(AppConstants.HEADER_TRACE_ID, responseGroupId);
                params.put("responseGroupId", responseGroupId);
            }

            functionService.execMethod(taskConfig.getCallbackMethod(), params);
        } catch (Exception e) {
            log.error("消费【BI批量计算处理完成】消息失败!", e);
        } finally {
            MDC.clear();
        }

    }
}

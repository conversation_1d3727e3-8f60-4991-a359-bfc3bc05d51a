package com.trax.lenz.service.message.produce.strategy;

/**
 * @ClassName MessageStrategyService
 * @Description: 消息发送
 * <AUTHOR>
 * @Date 2024-10-24-13:41
 **/

public interface MessageStrategyService<T> {

    /**
     * 判断是否支持
     *
     * @param pluginType
     * @return
     */
    boolean support(String pluginType);

    /**
     * 发送消息
     *
     * @param topic
     * @param message
     * @return
     */
    String sendMessage(String topic, T message);

}

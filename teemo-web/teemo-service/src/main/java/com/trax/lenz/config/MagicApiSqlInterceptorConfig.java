package com.trax.lenz.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.ssssssss.magicapi.core.context.RequestEntity;
import org.ssssssss.magicapi.modules.db.BoundSql;
import org.ssssssss.magicapi.modules.db.inteceptor.SQLInterceptor;

import java.util.Arrays;

/**
 * @ClassName MagicApiSqlInterceptorConfig
 * @Description:
 * <AUTHOR>
 * @Date 2024-01-05-14:37
 **/
@Component
@Slf4j
public class MagicApiSqlInterceptorConfig implements SQLInterceptor {

    @Override
    public void preHandle(BoundSql boundSql, RequestEntity requestEntity) {
        log.info("执行的SQL参数:" + Arrays.toString(boundSql.getParameters()));
        log.info("执行的SQL:" + boundSql.getSql());
    }

}

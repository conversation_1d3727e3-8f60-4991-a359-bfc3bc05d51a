package com.trax.lenz.handler;

import com.trax.lenz.common.core.domain.R;
import com.trax.lenz.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.DispatcherServlet;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * 使用 @ControllerAdvice & ResponseBodyAdvice 拦截Controller方法默认返回参数，统一处理返回值/响应体
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
@ConditionalOnClass (DispatcherServlet.class)
public class RestControllerExceptionHandler {
	
	public static final String TRACE_ID = "traceId";
	
	/**
	 * 处理自定义的业务异常
	 *
	 * @param e       异常对象
	 * @param request request
	 * @return 错误结果
	 */
	
	@ExceptionHandler (BusinessException.class)
	public R bizExceptionHandler(BusinessException e, HttpServletRequest request) {
		log.error("发生异常: {}", e.getMessage(), e);
		String traceId = null != request.getHeader(TRACE_ID) ? request.getHeader(TRACE_ID) : request.getParameter(TRACE_ID);
		if (null == traceId || "".equals(traceId)) {
			traceId = MDC.get(TRACE_ID);
		}
		if (null == traceId || "".equals(traceId)) {
			traceId = "TRES-" + UUID.randomUUID().toString();
		}
		return R.fail(e.getCode().intValue(), e.getMessage()).path(request.getRequestURL().toString()).traceId(traceId).data(new Object());
	}
	
	
	/**
	 * 处理自定义的业务异常
	 *
	 * @param e       异常对象
	 * @param request request
	 * @return 错误结果
	 */
	
	@ExceptionHandler (Exception.class)
	public R exceptionHandler(Exception e, HttpServletRequest request) {
		log.error("发生异常: {}", e.getMessage(), e);
		String traceId = null != request.getHeader(TRACE_ID) ? request.getHeader(TRACE_ID) : request.getParameter(TRACE_ID);
		if (null == traceId || "".equals(traceId)) {
			traceId = MDC.get(TRACE_ID);
		}
		if (null == traceId || "".equals(traceId)) {
			traceId = "TRES-" + UUID.randomUUID().toString();
		}
		return R.fail(500, e.getMessage()).path(request.getRequestURL().toString()).traceId(traceId).data(new Object());
	}
	
}

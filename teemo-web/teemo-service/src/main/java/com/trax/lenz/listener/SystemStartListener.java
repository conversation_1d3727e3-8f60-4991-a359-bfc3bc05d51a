package com.trax.lenz.listener;

import com.trax.lenz.common.core.util.SpringUtils;
import com.trax.lenz.listener.kafka.KafkaMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 启动监听
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SystemStartListener implements ApplicationRunner {

    @Value("${spring.kafka.enabled:false}")
    private boolean kafkaEnabled;

    @Override
    public void run(ApplicationArguments args) {
        new Thread(this::startListener).start();
    }

    private void startListener() {
        try {
            log.info("服务启动完毕！5秒后启动kafka监听Listener！");
            Thread.sleep(5 * 1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        // 启动kafka消费者
        if (kafkaEnabled) {
            KafkaMessageListener kafkaMessageListener = SpringUtils.getBean(KafkaMessageListener.class);
            kafkaMessageListener.start();
        }
    }
}

package com.trax.lenz.service.submit;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.trax.lenz.common.core.id.SnowFlakeFactory;
import com.trax.lenz.common.utils.HttpUtil;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.dto.HttpResponse;
import com.trax.lenz.dto.request.submit.SnowBeerBatchSubmitReq;
import com.trax.lenz.entity.SnowbeerResultDetail;
import com.trax.lenz.service.impl.SnowbeerResultDetailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @ClassName identifySubmitService
 * @Description: 项目识别提交
 * <AUTHOR>
 * @Date 2024-04-12-16:35
 **/
@Service
@Slf4j
@RefreshScope
public class identifySubmitService {

    @Value("${custom.config.snowbeer.submitUrl}")
    private String submitUrl;

    @Value("${custom.config.snowbeer.intervalTime}")
    private String intervalTime;

    private final SnowbeerResultDetailServiceImpl snowbeerResultDetailService;

    private final SnowFlakeFactory snowFlakeFactory;

    public identifySubmitService(SnowbeerResultDetailServiceImpl snowbeerResultDetailService, SnowFlakeFactory snowFlakeFactory) {
        this.snowbeerResultDetailService = snowbeerResultDetailService;
        this.snowFlakeFactory = snowFlakeFactory;
    }

    @Async(value = "businessExecutor")
    protected void submitIdentify(SnowBeerBatchSubmitReq req, Map<String, List<SnowbeerResultDetail>> visitCodeMap) {
        // 开始异步提交答卷
        for (List<SnowbeerResultDetail> detailList : visitCodeMap.values()) {
            try {
                String responseId = snowBeerSubmit(req, detailList);
                log.info("异步提交答卷成功, responseId:{}, 当前提交间隔:{}", responseId, intervalTime);
                SnowbeerResultDetail snowbeerResultDetail = new SnowbeerResultDetail();
                snowbeerResultDetail.setResponseId(responseId);
                snowbeerResultDetailService.updateByVisitCode(detailList.get(0).getVisitCode(), snowbeerResultDetail);
                Thread.sleep(Long.parseLong(intervalTime));
            } catch (Exception e) {
                log.error("异步提交答卷失败", e);
            }
        }
    }

    /**
     * 雪花项目组装提交答卷
     *
     * @param req
     * @param detailList
     * @return
     */
    private String snowBeerSubmit(SnowBeerBatchSubmitReq req, List<SnowbeerResultDetail> detailList) {
        List<Object> imageList = Lists.newArrayList();
        for (SnowbeerResultDetail detail : detailList) {
            Map<String, String> image = Maps.newHashMap();
            image.put("imageId", detail.getCustomerImageId());
            image.put("imageUrl", detail.getImageUrl());
            imageList.add(image);
        }
        Map<String, Object> submitMap = Maps.newHashMap();
        submitMap.put("taskId", req.getTaskId());
        submitMap.put("questionId", req.getQuestionId());
        submitMap.put("customerRequestId", snowFlakeFactory.nextIdStr());
        submitMap.put("imageList", imageList);
        HttpResponse post = HttpUtil.post(submitUrl, JsonUtil.toJsonString(submitMap), null);
        log.info("雪花项目提交答卷结果:{}", JsonUtil.toJsonString(post));
        String response = post.getResponse();
        String responseId = JSONObject.parseObject(JSONObject.parseObject(response).get("data").toString()).get("responseId").toString();
        log.info("雪花项目提交答卷成功, responseId:{}", responseId);
        return responseId;
    }
}

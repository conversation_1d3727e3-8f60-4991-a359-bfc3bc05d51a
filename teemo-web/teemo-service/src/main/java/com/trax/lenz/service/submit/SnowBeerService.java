package com.trax.lenz.service.submit;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trax.lenz.common.core.exception.BusinessException;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.dto.common.SnowBeerBatchSubmitFileDTO;
import com.trax.lenz.dto.request.submit.SnowBeerBatchSubmitReq;
import com.trax.lenz.entity.SnowbeerResultDetail;
import com.trax.lenz.entity.TaskConfig;
import com.trax.lenz.listener.excel.SnowBeerBatchSubmitExcelListener;
import com.trax.lenz.service.impl.SnowbeerResultDetailServiceImpl;
import com.trax.lenz.service.impl.TaskConfigServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName SnowBeerService
 * @Description: 雪花定制批量提交答卷
 * <AUTHOR>
 * @Date 2024-04-01-15:53
 **/
@Service
@Slf4j
@RefreshScope
public class SnowBeerService {

    private final SnowbeerResultDetailServiceImpl snowbeerResultDetailService;

    private final TaskConfigServiceImpl taskConfigService;

    private final identifySubmitService identifySubmitService;

    public SnowBeerService(SnowbeerResultDetailServiceImpl snowbeerResultDetailService, TaskConfigServiceImpl taskConfigService, com.trax.lenz.service.submit.identifySubmitService identifySubmitService) {
        this.snowbeerResultDetailService = snowbeerResultDetailService;
        this.taskConfigService = taskConfigService;
        this.identifySubmitService = identifySubmitService;
    }

    public void batchSubmit(SnowBeerBatchSubmitReq req, MultipartFile file) {
        TaskConfig one = taskConfigService.getOne(req.getTaskId(), 1);
        if (Objects.isNull(one)) {
            throw new BusinessException("上传任务不存在, 请检查!");
        }
        log.info("开始解析数据");
        List<SnowBeerBatchSubmitFileDTO> snowBeerBatchSubmitFileDTOList = analysisData(file);
        log.info("解析数据完成, 共{}条数据", snowBeerBatchSubmitFileDTOList.size());
        List<SnowbeerResultDetail> snowbeerResultDetailList = Lists.newArrayList();
        snowBeerBatchSubmitFileDTOList.forEach(dto -> {
            SnowbeerResultDetail snowbeerResultDetail = new SnowbeerResultDetail();
            BeanUtil.copyProperties(dto, snowbeerResultDetail);
            snowbeerResultDetail.setCustomerImageId(dto.getImageUrl().substring(dto.getImageUrl().lastIndexOf("/") + 1, dto.getImageUrl().lastIndexOf(".")));
            snowbeerResultDetailList.add(snowbeerResultDetail);
        });
        log.info("开始批量插入数据, snowbeerResultDetailList:{}", JsonUtil.toJsonString(snowbeerResultDetailList));
        // 分批插入
        List<List<SnowbeerResultDetail>> partitionList = com.google.common.collect.Lists.partition(snowbeerResultDetailList, 1000);
        for (List<SnowbeerResultDetail> snowbeerResultDetails : partitionList) {
            snowbeerResultDetailService.batchInsert(snowbeerResultDetails);
        }
        Map<String, List<SnowbeerResultDetail>> visitCodeMap = snowbeerResultDetailList.stream().collect(Collectors.groupingBy(SnowbeerResultDetail::getVisitCode));
        identifySubmitService.submitIdentify(req, visitCodeMap);
        log.info("识别提交完成");
    }

    /**
     * 解析数据
     *
     * @param file
     * @return
     */
    private List<SnowBeerBatchSubmitFileDTO> analysisData(MultipartFile file) {
        SnowBeerBatchSubmitExcelListener snowBeerBatchSubmitExcelListener = new SnowBeerBatchSubmitExcelListener();
        try {
            ExcelReader excelReader = EasyExcel.read(file.getInputStream(), SnowBeerBatchSubmitFileDTO.class, snowBeerBatchSubmitExcelListener).autoTrim(true).autoCloseStream(true).build();
            ReadSheet readSheet = EasyExcel.readSheet(0).headRowNumber(3).build();
            excelReader.read(readSheet);
            List<SnowBeerBatchSubmitFileDTO> list = snowBeerBatchSubmitExcelListener.getList();
            excelReader.finish();
            return list;
        } catch (Exception e) {
            log.info("解析数据失败", e);
        }
        return new ArrayList<>();
    }


    public void compensationExecution(SnowBeerBatchSubmitReq req) {
        if (CollectionUtils.isNotEmpty(req.getVisitCodeList())) {
            LambdaQueryWrapper<SnowbeerResultDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(SnowbeerResultDetail::getVisitCode, req.getVisitCodeList());
            List<SnowbeerResultDetail> list = snowbeerResultDetailService.list(wrapper);
            Map<String, List<SnowbeerResultDetail>> visitCodeMap = list.stream().collect(Collectors.groupingBy(SnowbeerResultDetail::getVisitCode));
            identifySubmitService.submitIdentify(req, visitCodeMap);
        } else {
            LambdaQueryWrapper<SnowbeerResultDetail> wrapper = new LambdaQueryWrapper<>();
            wrapper.isNull(SnowbeerResultDetail::getResponseId);
            List<SnowbeerResultDetail> list = snowbeerResultDetailService.list(wrapper);
            Map<String, List<SnowbeerResultDetail>> visitCodeMap = list.stream().collect(Collectors.groupingBy(SnowbeerResultDetail::getVisitCode));
            identifySubmitService.submitIdentify(req, visitCodeMap);
        }
    }
}



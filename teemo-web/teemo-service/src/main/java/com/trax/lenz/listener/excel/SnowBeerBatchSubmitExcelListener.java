package com.trax.lenz.listener.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.google.common.collect.Lists;
import com.trax.lenz.dto.common.SnowBeerBatchSubmitFileDTO;
import lombok.Data;

import java.util.List;

/**
 * @ClassName SnowBeerBatchSubmitExcelListener
 * @Description:
 * <AUTHOR>
 * @Date 2024-04-01-16:35
 **/
@Data
public class SnowBeerBatchSubmitExcelListener extends AnalysisEventListener<SnowBeerBatchSubmitFileDTO> {

    private List<SnowBeerBatchSubmitFileDTO> list = Lists.newArrayList();


    @Override
    public void invoke(SnowBeerBatchSubmitFileDTO snowBeerBatchSubmitFileDTO, AnalysisContext analysisContext) {
        list.add(snowBeerBatchSubmitFileDTO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}

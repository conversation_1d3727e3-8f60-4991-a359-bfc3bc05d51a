package com.trax.lenz.service.message.produce.strategy;

import com.fasterxml.jackson.core.type.TypeReference;
import com.trax.lenz.api.dto.common.MessageDTO;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.dto.bi.BiDataFinish;
import com.trax.lenz.entity.BatchIdentifyRequest;
import com.trax.lenz.enums.IdentifyStatus;
import com.trax.lenz.service.impl.BatchIdentifyRequestServiceImpl;
import com.trax.lenz.service.message.produce.service.impl.BiMessageServiceImpl;
import com.trax.lenz.service.message.produce.service.impl.PpzMessageServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @ClassName MessageStrategy
 * @Description: 消息策略
 * <AUTHOR>
 * @Date 2024-10-24-14:57
 **/
@Slf4j
@RefreshScope
@Component
public class MessageStrategy {

    private final List<MessageStrategyService> messageStrategyServices;

    private final BiMessageServiceImpl biMessageService;

    private final PpzMessageServiceImpl ppzMessageService;

    private final BatchIdentifyRequestServiceImpl batchIdentifyRequestService;

//    public MessageStrategy(List<MessageStrategyService> messageStrategyServices, BiMessageServiceImpl biMessageService, BatchIdentifyRequestServiceImpl batchIdentifyRequestService) {
//        this.messageStrategyServices = messageStrategyServices;
//        this.biMessageService = biMessageService;
//        this.batchIdentifyRequestService = batchIdentifyRequestService;
//    }

    public MessageStrategy(List<MessageStrategyService> messageStrategyServices, BiMessageServiceImpl biMessageService, PpzMessageServiceImpl ppzMessageService, BatchIdentifyRequestServiceImpl batchIdentifyRequestService) {
        this.messageStrategyServices = messageStrategyServices;
        this.biMessageService = biMessageService;
        this.ppzMessageService = ppzMessageService;
        this.batchIdentifyRequestService = batchIdentifyRequestService;
    }

    /**
     * 发送BI批量计算消息
     *
     * @param message
     * @return
     */
    public boolean sendBiBatchMessage(String message) {
        boolean flag = biMessageService.sendMessage(message);
        if (flag) {
            BiDataFinish biDataFinish = JsonUtil.parseObject(message, new TypeReference<BiDataFinish>() {
            });
            String responseGroupId = biDataFinish.getResponseGroupId();
            // 保存消息流水
            BatchIdentifyRequest batchIdentifyRequest = new BatchIdentifyRequest();
            batchIdentifyRequest.setBiReqContent(JsonUtil.toJsonString(message));
            batchIdentifyRequest.setStatus(IdentifyStatus.RECOGNITION_ING.getCode());
            batchIdentifyRequestService.update(responseGroupId, batchIdentifyRequest);
        } else {
            log.info("消息发送失败，消息内容：{}", message);
        }
        return flag;
    }

    /**
     * 发送PPZ识别结果消息
     *
     * @param message
     * @return
     */
    public boolean sendPpzBatchMessage(String message) {
        boolean flag = ppzMessageService.sendMessage(message);
        return flag;
    }


    public boolean sendMessage(List<MessageDTO> messageDTOS) {
        if (CollectionUtils.isEmpty(messageDTOS)) {
            return Boolean.FALSE;
        }
        messageDTOS.stream().forEach(messageDTO -> {
            log.info("消息内容：pluginType={}, topic={}, message={}", messageDTO.getPluginType(), messageDTO.getTopic(), messageDTO.getMessage());
            messageStrategyServices.stream().forEach(messageStrategyService -> {
                if (messageStrategyService.support(messageDTO.getPluginType())) {
                    String returnMessageInfo = messageStrategyService.sendMessage(messageDTO.getTopic(), messageDTO.getMessage());
                    log.info("消息发送结果：pluginType={}, topic={}, message={}, returnMessageInfo={}", messageDTO.getPluginType(), messageDTO.getTopic(), messageDTO.getMessage(), returnMessageInfo);
                }
            });
        });
        return Boolean.TRUE;
    }
}

package com.trax.lenz.config.prop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @ClassName CsutomerProperties
 * @Description:
 * <AUTHOR>
 * @Date 2024-10-23-19:34
 **/
@Data
@ConfigurationProperties(prefix = "custom")
@Component
public class CustomProperties {

    private Compress compress;

    private String sslTruststoreLocation;

    private String javaSecurityAuthLoginConfig;

    private Map<String, String> domains;

    private Map<String, String> callbackUrlMap;

    @Data
    public static class Compress {

        private Boolean enabledLowerQuality = false;

        private Float quality;
    }
}

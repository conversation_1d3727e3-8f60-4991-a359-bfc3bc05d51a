package com.trax.lenz.listener.kafka;

import com.trax.lenz.ServiceApplication;
import com.trax.lenz.common.constants.AppConstants;
import com.trax.lenz.common.utils.JsonUtil;
import com.trax.lenz.config.prop.KafkaProperties;
import com.trax.lenz.service.message.consume.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRebalanceListener;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.common.TopicPartition;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName KafkaMessageListener
 * @Description: kafka消费监听器
 * <AUTHOR>
 * @Date 2024-10-23-17:44
 **/
@Slf4j
@Component
@ConditionalOnProperty(value = "spring.kafka.enabled", havingValue = AppConstants.TRUE_STR)
public class KafkaMessageListener {

    private final List<MessageHandler> consumerHandlers;

    private final Properties consumerConfigs;

    private final KafkaProperties kafkaProperties;

    public KafkaMessageListener(List<MessageHandler> consumerHandlers, Properties consumerConfigs, KafkaProperties kafkaProperties) {
        this.consumerHandlers = consumerHandlers;
        this.consumerConfigs = consumerConfigs;
        this.kafkaProperties = kafkaProperties;
    }

    public void start() {
        Set<String> list = kafkaProperties.getConsumer().getTopics().entrySet().stream().map(Map.Entry::getValue).collect(Collectors.toSet());
        for (String topic : list) {
            new Thread(topic + "-listener-thread") {
                @Override
                public void run() {
                    startConsumer(topic);
                }
            }.start();
        }
    }

    private void startConsumer(String topic) {
        log.info("Kafka队列监听器启动成功！topic={}, consumerConfigs={}", topic, JsonUtil.toJsonString(consumerConfigs));
        org.apache.kafka.clients.consumer.KafkaConsumer<String, String> consumer = new org.apache.kafka.clients.consumer.KafkaConsumer<>(consumerConfigs);
        consumer.subscribe(Arrays.asList(topic), new ConsumerRebalanceListener() {
            @Override
            public void onPartitionsRevoked(Collection<TopicPartition> partitions) {
                //在再平衡开始之前和消费者停止读取消息之后被调用，可以在此处将位移提交。这里的partitions表示的是再平衡之前的分区
                consumer.commitSync();
            }

            @Override
            public void onPartitionsAssigned(Collection<TopicPartition> partitions) {
                //这个方法在重新分配分区之后和开始消费消息之前被调用，partitions表示的是在平衡后分配的分区
                StringBuffer sb = new StringBuffer();
                partitions.forEach(topicPartition -> {
                    sb.append(topicPartition.topic());
                    sb.append("|");
                    sb.append(topicPartition.partition());
                    sb.append(";");
                });
                log.info("kafka触发Rebalance！");
            }
        });
        while (true) {
            if (ServiceApplication.IS_STOP_SERVICE) {
                log.info("服务即将重启，kafka消息监听器关闭！");
                return;
            }
            long time = System.currentTimeMillis();
            log.info("获取消息! time={}", time);
            ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(5));
            for (ConsumerRecord<String, String> record : records) {
                String messageContent = record.value();
                log.info("收到Kafka消息! topic={}, offset={}, key={}, value={}", record.topic(), record.offset(), record.key(), record.value());
                try {
                    consumerHandlers.forEach(consumerHandler -> {
                        if (consumerHandler.support(record.topic())) {
                            consumerHandler.handle(messageContent);
                        }
                    });
                } catch (Exception e) {
                    log.error("消息处理异常！", e);
                }
            }
            consumer.commitSync();
            log.info("提交offset! time={}", time);
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                log.error("Thread.sleep error!", e);
                Thread.currentThread().interrupt();
            }
        }
    }
}

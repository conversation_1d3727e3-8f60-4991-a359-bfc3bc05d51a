package com.trax.lenz.http;

/**
 * 接口返回信息封装
 * <AUTHOR>
 */

public enum RestCode {

  /**
   * 失败
   */
  FAIL(500, "server.error"),
  /**
   * 任务未找到
   */
  TASK_NOT_FOUND(501, "task.not.found"),
  /**
   * AI任务
   */
  PARAM_ERROR(400, "AI任务"),
  ;
  private final int code;
  private final String msgKey;

  RestCode(int code, String msgKey) {
    this.code = code;
    this.msgKey = msgKey;
  }

  public int getCode() {
    return code;
  }

  public String getMsgKey() {
    return msgKey;
  }
}

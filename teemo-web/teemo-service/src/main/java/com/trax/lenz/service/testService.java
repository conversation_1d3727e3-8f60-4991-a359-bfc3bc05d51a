//package com.trax.lenz.service;
//
//import com.trax.lenz.common.core.id.SnowFlakeFactory;
//import com.trax.lenz.common.utils.ApimSignUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.codec.digest.DigestUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.core.io.FileSystemResource;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.stereotype.Service;
//import org.springframework.util.LinkedMultiValueMap;
//import org.springframework.util.MultiValueMap;
//import org.springframework.web.client.RestTemplate;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.BufferedReader;
//import java.io.DataOutputStream;
//import java.io.File;
//import java.io.IOException;
//import java.io.InputStreamReader;
//import java.io.UnsupportedEncodingException;
//import java.net.HttpURLConnection;
//import java.net.URL;
//import java.nio.charset.StandardCharsets;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.security.NoSuchAlgorithmException;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import static com.trax.lenz.common.utils.ApimSignUtil.sign;
//import static testService.CONST_SUBSCRIPTIONSECRET;
//
///**
// * @ClassName testService
// * @Description:
// * <AUTHOR>
// * @Date 2024-11-12-11:21
// **/
//@Service
//@Slf4j
//public class testService {
//
//    private final SnowFlakeFactory snowFlakeFactory;
//
//    @Value("${fileService.uploadUrl}")
//    private String uploadUrl;
//
//    @Value("${fileService.subscriptionKey}")
//    private String subscriptionKey;
//
//    @Value("${fileService.subscriptionSecret}")
//    private String subscriptionSecret;
//
//
//    private static final String CONST_SUBSCRIPTIONSECRET = "subscriptionSecret";
//    private static final String CONST_SUBSCRIPTIONKEY = "subscriptionKey";
//    private static final String CONST_PUBLIC = "public";
//    private static final String CONST_TIMESTAMP = "timestamp";
//    private static final String CONST_CONTEXT = "context";
//
//    public testService(SnowFlakeFactory snowFlakeFactory) {
//        this.snowFlakeFactory = snowFlakeFactory;
//    }
//
//    public void main(String[] args) throws UnsupportedEncodingException, NoSuchAlgorithmException {
//        // 构建基本参数
//        String timestamp = String.valueOf(System.currentTimeMillis());
//        String api_key = "ghr-golden-hand-psd-100641";  // 替换为实际的API密钥
//        String secret = "sTGxmiKbmqXV";  // 替换为实际的秘钥
//        String subscriptionKey = "f0566352135c903ebca930deac1c1ae480f37317";  // 订阅key
//        String publicFlag = "true";  // 设置为public
//
//        // 文件对象
//        File file = new File("/path/to/your/file.jpg");
//
//        // 构建请求体
//        LinkedMultiValueMap<String, Object> bodyMap = new LinkedMultiValueMap<>();
//        bodyMap.add("file", new FileSystemResource(file));
//        bodyMap.add("subscriptionKey", subscriptionKey);
//        bodyMap.add("public", publicFlag);
//        bodyMap.add("timestamp", timestamp);
//
//        // 生成签名参数
//        Map<String, String> params = new HashMap<>();
//        params.put("api_key", api_key);
//        params.put("subscriptionKey", subscriptionKey);
//        params.put("public", publicFlag);
//        params.put("timestamp", timestamp);
//
//        // 生成签名
//        String signature = ApimSignUtil.sign(params, secret);
//
//        // 将签名添加到请求体
//        bodyMap.add("signature", signature);
//
//        // 设置请求头
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("Content-Type", MediaType.MULTIPART_FORM_DATA_VALUE);
//        headers.set("Ocp-Apim-Subscription-Key", subscriptionKey);
//
//        // 创建请求 URL
//        String callbackUrl = "https://storage-qa.pg.com.cn/v2/files";  // 替换为实际环境的URL
//
//        // 构建 HttpEntity
//        HttpEntity<LinkedMultiValueMap<String, Object>> requestEntity = new HttpEntity<>(bodyMap, headers);
//
//        // 使用 RestTemplate 发送请求
//        RestTemplate restTemplate = new RestTemplate();
//        try {
//            ResponseEntity<String> response = restTemplate.postForEntity(callbackUrl, requestEntity, String.class);
//            System.out.println("Response: " + response.getBody());
//        } catch (Exception e) {
//            System.out.println("Error occurred while uploading file: " + e.getMessage());
//        }
//    }
//}
//
//public String uploadFile(MultipartFile file, String fileName, String isPublic) {
//    log.info("进入文件上传逻辑====");
//
//    try {
//        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
//        Long timeMilis = System.currentTimeMillis();
//
//        Map<String, String> signMap = new HashMap<>();
//        signMap.put(CONST_SUBSCRIPTIONKEY, subscriptionKey);
//        signMap.put(CONST_PUBLIC, isPublic);
//        signMap.put(CONST_TIMESTAMP, String.valueOf(timeMilis));
//        signMap.put(CONST_SUBSCRIPTIONSECRET, subscriptionSecret);
//        if (fileName != null) {
//            param.add("name", fileName);
//        }
//
//        param.add("signature", signature(signMap));
//        param.add(CONST_SUBSCRIPTIONKEY, subscriptionKey);
//        param.add(CONST_PUBLIC, isPublic);
//        param.add(CONST_TIMESTAMP, String.valueOf(timeMilis));
//
//        String contentType = file.getContentType();
//        //如果Excel文件后缀是xlsx，则采用application/vnd.ms-excel
//        if ("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(contentType)) {
//            contentType = "application/vnd.ms-excel";
//        }
////            vo = requestUtils.fileUploadRequest(param, file, uploadUrl, UploadFileVO.class, contentType);
//
//    } catch (Exception e) {
//        log.info("文件上传失败，正在重试=======");
//        log.error("file upload error==", e);
////            throw new BussinessException("7", "文件上传异常,请重试");
//    }
//
//    return vo;
//
//}
//
//public static HttpResponse postFile(String requestUrl, File file, Map<String, String> headers) throws IOException {
//    URL url = new URL(requestUrl);
//    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//    connection.setRequestMethod("POST");
//    connection.setDoOutput(true);
//    connection.setDoInput(true);
//    connection.setUseCaches(false);
//    connection.setRequestProperty("Connection", "Keep-Alive");
//    connection.setRequestProperty("Charset", "UTF-8");
//
//    // 设置请求头
//    for (Map.Entry<String, String> entry : headers.entrySet()) {
//        connection.setRequestProperty(entry.getKey(), entry.getValue());
//    }
//
//    // 构建 multipart/form-data 请求体
//    String boundary = "----WebKitFormBoundary7MA4YWxkTrZu0gW";
//    connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
//
//    try (DataOutputStream dos = new DataOutputStream(connection.getOutputStream())) {
//        StringBuilder sb = new StringBuilder();
//        sb.append("--").append(boundary).append("\r\n");
//        sb.append("Content-Disposition: form-data; name=\"file\"; filename=\"").append(file.getName()).append("\"\r\n");
//        sb.append("Content-Type: application/octet-stream\r\n\r\n");
//        dos.write(sb.toString().getBytes());
//
//        byte[] bytes = Files.readAllBytes(Paths.get(file.getAbsolutePath()));
//        dos.write(bytes);
//        dos.write("\r\n".getBytes());
//
//        dos.write(("--" + boundary + "--\r\n").getBytes());
//        dos.flush();
//    }
//
//    // 获取响应
//    int responseCode = connection.getResponseCode();
//    String responseMessage = connection.getResponseMessage();
//    String responseBody = "";
//
//    try (BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
//        String inputLine;
//        StringBuilder content = new StringBuilder();
//        while ((inputLine = in.readLine()) != null) {
//            content.append(inputLine);
//        }
//        responseBody = content.toString();
//    }
//
//    connection.disconnect();
//
//    return new HttpResponse(responseCode, responseMessage, responseBody);
//}
//
//public static class HttpResponse {
//    private int responseCode;
//    private String responseMessage;
//    private String responseBody;
//
//    public HttpResponse(int responseCode, String responseMessage, String responseBody) {
//        this.responseCode = responseCode;
//        this.responseMessage = responseMessage;
//        this.responseBody = responseBody;
//    }
//
//    public int getResponseCode() {
//        return responseCode;
//    }
//
//    public String getResponseMessage() {
//        return responseMessage;
//    }
//
//    public String getResponseBody() {
//        return responseBody;
//    }
//}
//
///**
// * 生成签名
// *
// * @param paramsMap 签名入参
// * @return 签名
// */
//public String signature(Map<String, String> paramsMap) {
//    try {
//        List<String> list = new ArrayList<>();
//        for (Map.Entry<String, String> element : paramsMap.entrySet()) {
//            list.add(concatParamStr(element.getKey(), element.getValue()));
//        }
//        StringBuilder sb = new StringBuilder();
//        Collections.sort(list);
//        for (String sigParam : list) {
//            sb.append(sigParam);
//        }
//        return DigestUtils.sha1Hex(sb.toString().getBytes(StandardCharsets.UTF_8.name()));
//    } catch (Exception e) {
//
//    }
//    return "";
//}
//
//public static String concatParamStr(String key, String value) {
//    return key.concat("=").concat(value);
//}
//
//}

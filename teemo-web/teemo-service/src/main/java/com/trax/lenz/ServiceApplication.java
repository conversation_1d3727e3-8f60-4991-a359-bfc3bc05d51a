package com.trax.lenz;


import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, UserDetailsServiceAutoConfiguration.class})
@MapperScan("com.trax.lenz.mapper")
@EnableFeignClients
@EnableAsync
@EnableScheduling
@EnableCaching
public class ServiceApplication {

    public volatile static boolean IS_STOP_SERVICE = false;

    public static void main(String[] args) {
        SpringApplication.run(ServiceApplication.class, args);
        log.info("=========== TEEMO 框架服务启动成功 ===========");
    }
}

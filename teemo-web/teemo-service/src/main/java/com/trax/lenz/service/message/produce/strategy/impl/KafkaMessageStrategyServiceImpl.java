package com.trax.lenz.service.message.produce.strategy.impl;

import com.alibaba.fastjson.JSONObject;
import com.trax.lenz.common.constants.AppConstants;
import com.trax.lenz.service.message.produce.strategy.MessageStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

/**
 * @ClassName KafkaMessageStrategyServiceImpl
 * @Description: Kafka 消息策略实现
 * <AUTHOR>
 * @Date 2024-10-24-13:44
 **/
@Slf4j
@RefreshScope
@ConditionalOnProperty(name = "spring.kafka.enabled", havingValue = AppConstants.TRUE_STR)
@Component
public class KafkaMessageStrategyServiceImpl implements MessageStrategyService<String> {

    private final KafkaTemplate<String, String> kafkaTemplate;

    public KafkaMessageStrategyServiceImpl(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    @Override
    public boolean support(String pluginType) {
        return AppConstants.KAFKA.equals(pluginType);
    }

    @Override
    public String sendMessage(String topic, String message) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("topic", topic);
        try {
            SendResult sendResult = kafkaTemplate.send(topic, message).get();
            int partition = sendResult.getRecordMetadata().partition();
            long offset = sendResult.getRecordMetadata().offset();
            log.info("kafka消息发送成功！topic:{}, partition:{}, offset:{}, messageJson:{}", topic, partition, offset, message);
            jsonObject.put("partition", partition);
            jsonObject.put("offset", offset);
        } catch (Exception e) {
            log.error("kafka消息发送异常！", e);
            jsonObject.clear();
        }
        return jsonObject.toJSONString();
    }
}

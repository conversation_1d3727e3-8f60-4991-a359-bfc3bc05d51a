package com.trax.lenz.service.message.produce.service.impl;

import com.google.common.collect.Lists;
import com.trax.lenz.api.dto.common.MessageDTO;
import com.trax.lenz.common.constants.AppConstants;
import com.trax.lenz.common.constants.MessageConstant;
import com.trax.lenz.config.prop.KafkaProperties;
import com.trax.lenz.service.impl.BatchIdentifyRequestServiceImpl;
import com.trax.lenz.service.message.produce.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName PpzMessageServiceImpl
 * @Description: PPZ消息生产器实现
 * <AUTHOR>
 * @Date 2024-10-24-15:00
 **/
@Service
@Slf4j
public class PpzMessageServiceImpl extends MessageService {

    @Value("#{'${mesaage.producer.ppzMessageSync}'.split(',')}")
    private List<String> ppzMessageSyncs;

    private final KafkaProperties kafkaProperties;

    private final BatchIdentifyRequestServiceImpl batchIdentifyRequestService;

    public PpzMessageServiceImpl(KafkaProperties kafkaProperties, BatchIdentifyRequestServiceImpl batchIdentifyRequestService) {
        this.kafkaProperties = kafkaProperties;
        this.batchIdentifyRequestService = batchIdentifyRequestService;
    }

    @Override
    public <T> List<MessageDTO> buildMessage(T message) {
        try {
            log.info("接收到发送【PPZ批量计算】消息! message={}", message);
            ArrayList<MessageDTO> messages = new ArrayList<>();
            ppzMessageSyncs.stream().forEach(ppzMessage -> {
                if (AppConstants.KAFKA.equals(ppzMessage)) {
                    MessageDTO build = MessageDTO.builder()
                            .pluginType(AppConstants.KAFKA)
                            .topic(kafkaProperties.getProducer().getTopics().get(MessageConstant.PPZ_MESSAGE_SYNC))
                            .message(message)
                            .build();
                    messages.add(build);
                }
            });
            return messages;
        } catch (Exception e) {
            log.error("【PPZ批量计算】消息发送失败! ", e);
        }
        return Lists.newArrayList();
    }
}
package com.trax.lenz.service.message.produce.service;

import com.trax.lenz.api.dto.common.MessageDTO;
import com.trax.lenz.service.message.produce.strategy.MessageStrategy;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @ClassName MessageService
 * @Description: 消息服务
 * <AUTHOR>
 * @Date 2024-10-24-15:34
 **/

public abstract class MessageService {

    @Autowired
    protected MessageStrategy messageStrategy;

    public <T> boolean sendMessage(T message) {
        List<MessageDTO> messageDTOS = buildMessage(message);
        return messageStrategy.sendMessage(messageDTOS);
    }

    public abstract <T> List<MessageDTO> buildMessage(T message);

}

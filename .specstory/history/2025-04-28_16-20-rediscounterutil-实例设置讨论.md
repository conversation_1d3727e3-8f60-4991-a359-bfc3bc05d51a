<!-- Generated by SpecStory -->

# RedisCounterUtil 实例设置讨论 (2025-04-29 00:20:25)

_**User**_

RedisCounterUtil
这个方法需要设置另一个 redis 实例  请帮我处理

---

_**Assistant**_

我来帮你修改 RedisCounterUtil 类，添加另一个 Redis 实例的支持。首先我需要查看一下当前的代码结构。


---

<details>
            <summary>Searched codebase "RedisTemplate configuration or bean definition" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `teemo-common/teemo-business/src/main/java/com/trax/lenz/config/AuditRedisConfig.java` | L20-64 |
| `teemo-common/teemo-business/src/main/java/com/trax/lenz/config/AuditRedisConfig.java` | L1-20 |
| `teemo-common/teemo-business/src/main/java/com/trax/lenz/config/prop/RedisProperties.java` | L1-42 |
| `teemo-web/teemo-service/src/main/resources/bootstrap.yml` | L1-65 |
| `teemo-common/teemo-business/src/main/java/com/trax/lenz/service/RedisTemplateService.java` | L1-46 |
| `teemo-web/teemo-manager/src/main/resources/bootstrap.yml` | L1-65 |
| `teemo-common/teemo-business/src/main/java/com/trax/lenz/config/AuditRedisConfig.java` | L64-78 |
| `teemo-common/teemo-business/src/main/java/com/trax/lenz/common/business/util/RedisCounterUtil.java` | L1-68 |
| `teemo-common/teemo-business/src/main/java/com/trax/lenz/service/RedisTemplateService.java` | L46-67 |
| `teemo-common/teemo-business/src/main/java/com/trax/lenz/common/business/util/RedisCounterUtil.java` | L68-94 |
| `teemo-web/teemo-service/src/main/java/com/trax/lenz/config/IdGenderConfig.java` | L1-39 |
| `teemo-web/teemo-service/src/main/java/com/trax/lenz/config/KafkaConfig.java` | L27-58 |
| `teemo-web/teemo-service/src/main/java/com/trax/lenz/config/JsonConfig.java` | L1-24 |
| `teemo-common/teemo-business/pom.xml` | L115-167 |
| `teemo-web/teemo-manager/src/main/java/com/trax/lenz/ManagerApplication.java` | L1-41 |
| `teemo-web/teemo-service/src/main/java/com/trax/lenz/config/KafkaConfig.java` | L1-27 |
| `teemo-web/teemo-service/src/main/java/com/trax/lenz/config/KafkaConfig.java` | L90-119 |
| `teemo-common/teemo-domain/src/main/java/com/trax/lenz/dto/redis/SendRedisProductDTO.java` | L1-26 |
| `teemo-common/teemo-business/src/main/java/com/trax/lenz/service/remote/AzathothService.java` | L31-59 |
| `teemo-web/teemo-manager/src/main/java/com/trax/lenz/config/MessageConfig.java` | L1-34 |
| `teemo-common/teemo-business/src/main/java/com/trax/lenz/service/PaladinService.java` | L32-83 |
| `teemo-common/teemo-domain/src/main/java/com/trax/lenz/dto/redis/SendRedisMessageDTO.java` | L1-25 |
| `teemo-web/teemo-service/src/main/java/com/trax/lenz/ServiceApplication.java` | L1-33 |
| `teemo-common/teemo-business/src/main/java/com/trax/lenz/config/OssConfig.java` | L1-30 |
| `teemo-web/teemo-service/src/main/java/com/trax/lenz/config/MessageConfig.java` | L1-34 |

</details>

---

根据搜索结果，我发现项目中已经有两个 Redis 实例的配置：`auditIngRedisTemplate` 和 `auditDongRedisTemplate`。我将修改 RedisCounterUtil 类以支持这两个 Redis 实例。

让我来修改 RedisCounterUtil 类：


---

```diff
  public class RedisCounterUtil {
  
-     @Resource
-     private RedisTemplate<String, Integer> redisTemplate;
+     @Resource(name = "auditIngRedisTemplate")
+     private RedisTemplate<String, Integer> auditIngRedisTemplate;
+ 
+     @Resource(name = "auditDongRedisTemplate")
+     private RedisTemplate<String, Integer> auditDongRedisTemplate;
  
      private static final String COUNTER_KEY_PREFIX = "_mn_repeat_backlog";
+ 
+     /**
+      * Redis 实例类型枚举
+      */
+     public enum RedisInstance {
+         AUDIT_ING,
+         AUDIT_DONG
+     }
  
      /**
       * @param initialValue 初始值
       * @param expireTime   过期时间（秒）
+      * @param instance     Redis 实例类型
       * @return 是否设置成功
       */
-     public boolean setCounter(String key, int initialValue, long expireTime) {
+     public boolean setCounter(String key, int initialValue, long expireTime, RedisInstance instance) {
          try {
              String redisKey = COUNTER_KEY_PREFIX + key;
+             RedisTemplate<String, Integer> redisTemplate = getRedisTemplate(instance);
              // 检查key是否已存在
              if (Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))) {
-                 log.info("计数器key已存在，不进行写入操作, key: {}", key);
+                 log.info("计数器key已存在，不进行写入操作, key: {}, instance: {}", key, instance);
                  return false;
              }
              redisTemplate.opsForValue().set(redisKey, initialValue, expireTime, TimeUnit.SECONDS);
              return true;
          } catch (Exception e) {
-             log.error("设置计数器初始值失败, key: {}, initialValue: {}, expireTime: {}", key, initialValue, expireTime, e);
+             log.error("设置计数器初始值失败, key: {}, initialValue: {}, expireTime: {}, instance: {}", 
+                 key, initialValue, expireTime, instance, e);
              return false;
          }
       * 增加计数器值（每次+1）
       *
-      * @param key 计数器键名
+      * @param key      计数器键名
+      * @param instance Redis 实例类型
       * @return 增加后的值
       */
-     public int incrementCounter(String key) {
+     public int incrementCounter(String key, RedisInstance instance) {
          try {
              String redisKey = COUNTER_KEY_PREFIX + key;
+             RedisTemplate<String, Integer> redisTemplate = getRedisTemplate(instance);
              Long value = redisTemplate.opsForValue().increment(redisKey);
              return value != null ? value.intValue() : 0;
          } catch (Exception e) {
-             log.error("增加计数器值失败, key: {}", key, e);
+             log.error("增加计数器值失败, key: {}, instance: {}", key, instance, e);
              return 0;
          }
       * 获取计数器当前值
       *
-      * @param key 计数器键名
+      * @param key      计数器键名
+      * @param instance Redis 实例类型
       * @return 当前值
       */
-     public int getCounterValue(String key) {
+     public int getCounterValue(String key, RedisInstance instance) {
          try {
              String redisKey = COUNTER_KEY_PREFIX + key;
+             RedisTemplate<String, Integer> redisTemplate = getRedisTemplate(instance);
              Object value = redisTemplate.opsForValue().get(redisKey);
              if (value != null) {
              return 0;
          } catch (Exception e) {
-             log.error("获取计数器当前值失败, key: {}", key, e);
+             log.error("获取计数器当前值失败, key: {}, instance: {}", key, instance, e);
              return 0;
          }
      }
+ 
+     /**
+      * 根据实例类型获取对应的 RedisTemplate
+      *
+      * @param instance Redis 实例类型
+      * @return RedisTemplate 实例
+      */
+     private RedisTemplate<String, Integer> getRedisTemplate(RedisInstance instance) {
+         return switch (instance) {
+             case AUDIT_ING -> auditIngRedisTemplate;
+             case AUDIT_DONG -> auditDongRedisTemplate;
+         };
+     }
  } 
```

---

我注意到有一些 linter 错误，这是因为使用了 Java 14 的 switch 表达式特性。让我修改一下代码以兼容较低版本的 Java：



---

```diff
       */
      private RedisTemplate<String, Integer> getRedisTemplate(RedisInstance instance) {
-         return switch (instance) {
-             case AUDIT_ING -> auditIngRedisTemplate;
-             case AUDIT_DONG -> auditDongRedisTemplate;
-         };
+         switch (instance) {
+             case AUDIT_ING:
+                 return auditIngRedisTemplate;
+             case AUDIT_DONG:
+                 return auditDongRedisTemplate;
+             default:
+                 throw new IllegalArgumentException("不支持的 Redis 实例类型: " + instance);
+         }
      }
  } 
```

---

我已经完成了 RedisCounterUtil 类的修改，主要变更包括：

1. 添加了两个 Redis 实例的注入：
   - `auditIngRedisTemplate`
   - `auditDongRedisTemplate`

2. 添加了 RedisInstance 枚举类来指定使用哪个 Redis 实例：
   ```java
   public enum RedisInstance {
       AUDIT_ING,
       AUDIT_DONG
   }
   ```

3. 修改了所有方法，添加了 instance 参数：
   - `setCounter(String key, int initialValue, long expireTime, RedisInstance instance)`
   - `incrementCounter(String key, RedisInstance instance)`
   - `getCounterValue(String key, RedisInstance instance)`

4. 添加了 `getRedisTemplate` 方法来根据实例类型获取对应的 RedisTemplate

现在你可以这样使用这个工具类：

```java
// 使用 auditIng Redis 实例
redisCounterUtil.setCounter("myKey", 0, 3600, RedisCounterUtil.RedisInstance.AUDIT_ING);
int value = redisCounterUtil.getCounterValue("myKey", RedisCounterUtil.RedisInstance.AUDIT_ING);

// 使用 auditDong Redis 实例
redisCounterUtil.setCounter("myKey", 0, 3600, RedisCounterUtil.RedisInstance.AUDIT_DONG);
int value = redisCounterUtil.getCounterValue("myKey", RedisCounterUtil.RedisInstance.AUDIT_DONG);
```

这样的修改使得 RedisCounterUtil 可以同时支持两个 Redis 实例，并且通过枚举类型来明确指定使用哪个实例，提高了代码的可维护性和可读性。

需要注意的是，使用这个工具类的地方需要相应地修改代码，添加 RedisInstance 参数。如果你需要我帮助修改使用这个工具类的其他代码，请告诉我。

---



---

